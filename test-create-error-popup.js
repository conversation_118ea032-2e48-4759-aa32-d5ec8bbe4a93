// Test script for the refactored createErrorPopup function

// Mock the chrome API
const chrome = {
  i18n: {
    getMessage: (key) => {
      // Mock localized strings
      const messages = {
        'errorTitle': 'Error',
        'closeButtonText': 'Close',
        'errorLoginRequired': '<PERSON><PERSON> is required to use this feature.',
        'errorExceedFreeTrialQuota': 'You have exceeded your free trial quota.',
        'errorExceedDailyQuota': 'You have exceeded your daily quota.',
        'errorExceedMsgLimit': 'You have exceeded the message limit.',
        'errorServiceUnavailable': 'Service is currently unavailable. Please try again later.',
        'errorAIGenerationFailed': 'AI generation failed. Please try again.',
        'pricingButton': 'View Plans',
        'loginFunBlocksButton': 'Login FunBlocks AI Account'
      };
      return messages[key] || key;
    }
  }
};

// Mock document functions
const document = {
  createElement: (tag) => {
    console.log(`Creating element: ${tag}`);
    return {
      id: '',
      className: '',
      style: {},
      appendChild: (child) => {
        console.log('Appending child:', child);
      },
      addEventListener: (event, handler) => {
        console.log(`Adding event listener for ${event}`);
      },
      setAttribute: (attr, value) => {
        console.log(`Setting attribute ${attr}=${value}`);
      },
      textContent: ''
    };
  },
  getElementById: (id) => {
    console.log(`Getting element by ID: ${id}`);
    return {
      classList: {
        add: (className) => {
          console.log(`Adding class ${className} to element with ID ${id}`);
        },
        remove: (className) => {
          console.log(`Removing class ${className} from element with ID ${id}`);
        }
      },
      disabled: false
    };
  }
};

// Mock window functions
const window = {
  open: (url) => {
    console.log(`Opening URL: ${url}`);
  }
};

// Mock helper functions
function getLocalizedString(key) {
  return chrome.i18n.getMessage(key);
}

function openLoginPage() {
  const funblocks_domain = 'funblocks.net';
  window.open(`https://app.${funblocks_domain}/#/login?source=extension`);
}

function openPricingPage() {
  window.open('https://app.funblocks.net/#/aiplans', '_blank');
}

function closePopup(popup, overlay) {
  console.log('Closing popup:', popup);
  console.log('Closing overlay:', overlay);
}

function createPopup(options) {
  console.log('Creating popup with options:', JSON.stringify(options, null, 2));
  
  // Call the content generator to test it
  const popupBody = {
    appendChild: (child) => {
      console.log('Appending child to popup body:', child);
    }
  };
  
  if (options.contentGenerator) {
    options.contentGenerator(popupBody);
  }
  
  // Return a mock popup object
  return {
    id: options.id,
    className: `ai-prompt-optimizer-popup ${options.type}-popup`,
    style: {}
  };
}

// The refactored createErrorPopup function
function createErrorPopup(errorCodeOrMessage) {
  // 将旧的错误消息转换为错误代码（向后兼容）
  let errorCode = errorCodeOrMessage;
  
  // 处理旧的错误消息格式（向后兼容）
  if (errorCodeOrMessage.includes && (errorCodeOrMessage.includes('Login required') || errorCodeOrMessage.includes('403'))) {
    errorCode = 'ERR_LOGIN_REQUIRED';
  } else if (errorCodeOrMessage === 'exceed_free_trial_quota') {
    errorCode = 'ERR_EXCEED_FREE_TRIAL_QUOTA';
  } else if (errorCodeOrMessage === 'exceed_daily_quota') {
    errorCode = 'ERR_EXCEED_DAILY_QUOTA';
  } else if (errorCodeOrMessage === 'exceed_msg_limit') {
    errorCode = 'ERR_EXCEED_MSG_LIMIT';
  } else if (errorCodeOrMessage.includes && errorCodeOrMessage.includes('Failed to request')) {
    errorCode = 'ERR_SERVICE_UNAVAILABLE';
  }
  
  console.log('Processing error code:', errorCode);
  
  // 根据错误代码获取格式化的错误消息
  let formattedErrorMessage;
  let buttons = [];
  
  // 处理特定错误代码
  switch (errorCode) {
    case 'ERR_EXCEED_FREE_TRIAL_QUOTA':
      formattedErrorMessage = getLocalizedString('errorExceedFreeTrialQuota') || 'You have exceeded your free trial quota.';
      // 添加价格按钮
      buttons = [
        {
          type: 'confirm',
          text: getLocalizedString('pricingButton') || 'View Plans',
          onClick: (_, popup) => {
            openPricingPage();
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
      
    case 'ERR_EXCEED_DAILY_QUOTA':
      formattedErrorMessage = getLocalizedString('errorExceedDailyQuota') || 'You have exceeded your daily quota.';
      // 添加价格按钮
      buttons = [
        {
          type: 'confirm',
          text: getLocalizedString('pricingButton') || 'View Plans',
          onClick: (_, popup) => {
            openPricingPage();
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
      
    case 'ERR_EXCEED_MSG_LIMIT':
      formattedErrorMessage = getLocalizedString('errorExceedMsgLimit') || 'You have exceeded the message limit.';
      // 添加价格按钮
      buttons = [
        {
          type: 'confirm',
          text: getLocalizedString('pricingButton') || 'View Plans',
          onClick: (_, popup) => {
            openPricingPage();
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
      
    case 'ERR_LOGIN_REQUIRED':
      formattedErrorMessage = getLocalizedString('errorLoginRequired') || 'Login is required to use this feature.';
      // 添加登录按钮
      buttons = [
        {
          type: 'confirm',
          text: getLocalizedString('loginFunBlocksButton') || 'Login FunBlocks AI Account',
          onClick: (_, popup) => {
            openLoginPage();
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
      
    case 'ERR_SERVICE_UNAVAILABLE':
      formattedErrorMessage = getLocalizedString('errorServiceUnavailable') || 'Service is currently unavailable. Please try again later.';
      // 默认关闭按钮
      buttons = [
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
      
    case 'ERR_INVALID_JSON':
    case 'ERR_GENERATION_FAILED':
      formattedErrorMessage = getLocalizedString('errorAIGenerationFailed') || 'AI generation failed. Please try again.';
      // 默认关闭按钮
      buttons = [
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
      
    default:
      // 对于未知错误代码，直接使用原始消息
      formattedErrorMessage = errorCodeOrMessage;
      // 默认关闭按钮
      buttons = [
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
  }
  
  return createPopup({
    type: 'error',
    id: 'ai-error-popup',
    title: getLocalizedString('errorTitle') || 'Error',
    contentGenerator: (popupBody) => {
      // 创建错误信息容器
      const errorContainer = document.createElement('div');
      errorContainer.className = 'ai-prompt-optimizer-popup-error-container';

      // 创建错误图标
      const errorIcon = document.createElement('div');
      errorIcon.className = 'ai-prompt-optimizer-popup-error-icon';

      // 创建错误文本
      const errorText = document.createElement('div');
      errorText.className = 'ai-prompt-optimizer-popup-text error-text';
      errorText.textContent = formattedErrorMessage;

      // 将错误图标和文本添加到容器中
      errorContainer.appendChild(errorIcon);
      errorContainer.appendChild(errorText);
      popupBody.appendChild(errorContainer);
    },
    buttons: buttons
  });
}

// Test the createErrorPopup function with different error codes
console.log('=== Testing with ERR_LOGIN_REQUIRED ===');
createErrorPopup('ERR_LOGIN_REQUIRED');

console.log('\n=== Testing with ERR_EXCEED_FREE_TRIAL_QUOTA ===');
createErrorPopup('ERR_EXCEED_FREE_TRIAL_QUOTA');

console.log('\n=== Testing with ERR_SERVICE_UNAVAILABLE ===');
createErrorPopup('ERR_SERVICE_UNAVAILABLE');

console.log('\n=== Testing with ERR_INVALID_JSON ===');
createErrorPopup('ERR_INVALID_JSON');

console.log('\n=== Testing with old error message format (Login required) ===');
createErrorPopup('Login required');

console.log('\n=== Testing with old error message format (exceed_free_trial_quota) ===');
createErrorPopup('exceed_free_trial_quota');

console.log('\n=== Testing with unknown error code ===');
createErrorPopup('UNKNOWN_ERROR');
