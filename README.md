# AI Prompt Optimizer Chrome Extension

A Chrome extension that adds a button to AI chat interfaces (ChatGPT, Gemini, Claude, DeepSeek) to optimize user prompts using an LLM backend service. Built with React and TypeScript.

## Features

- Adds an "Optimize Prompt" button to supported AI chat interfaces
- Sends the user's input to a backend service for optimization
- Replaces the input with the optimized prompt
- Supports internationalization (English and Chinese)

## Supported Platforms

- ChatGPT (chat.openai.com)
- Gemini (gemini.google.com)
- <PERSON> (claude.ai)
- DeepSeek (deepseek.com)

## Installation

### Development Mode

1. Clone this repository
2. Install dependencies: `npm install`
3. Build the extension: `npm run build`
4. Open Chrome and navigate to `chrome://extensions/`
5. Enable "Developer mode" in the top right corner
6. Click "Load unpacked" and select the `dist` directory from this repository

### Production Mode

1. Build the extension package: `npm run package`
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right corner
4. Drag and drop the generated `ai-prompt-optimizer.zip` file onto the page

## Development Workflow

```bash
# Install dependencies
npm install

# Generate icons from SVG to PNG (requires librsvg or Inkscape)
chmod +x convert-icons.sh
./convert-icons.sh

# Build for development with watch mode
npm run dev

# Build for production
npm run build

# Create a distributable package
npm run package

# Clean build artifacts and dependencies
npm run clean
```

## Configuration

Click on the extension icon in the Chrome toolbar to access settings:

- **API URL**: Set the URL of your backend optimization service
- **Language**: Choose between English and Chinese

## Backend API

You need to implement a backend service that accepts POST requests with the following structure:

```json
{
  "prompt": "Your original prompt text here"
}
```

And returns a response with the following structure:

```json
{
  "optimizedPrompt": "Your optimized prompt text here"
}
```

## Development

### Project Structure

- `src/manifest.json`: Extension configuration
- `src/content.js`: Content script that injects the button into AI chat interfaces
- `src/content.css`: Styles for the injected button
- `src/background.js`: Background service worker for handling API requests
- `src/popup/`: React components for the popup UI
  - `components/`: React components
  - `hooks/`: Custom React hooks
  - `styles/`: CSS styles
- `src/_locales/`: Internationalization files
- `src/images/`: Icons and graphics
  - `icon.svg`: Extension icon (SVG source) used for both extension and button
  - `icon16.png`, `icon48.png`, `icon128.png`: Generated PNG icons
- `webpack.config.js`: Webpack configuration
- `tsconfig.json`: TypeScript configuration
- `convert-icons.sh`: Script to convert SVG icons to PNG

## License

MIT
