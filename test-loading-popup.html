<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading Popup Test</title>
    <link rel="stylesheet" href="src/content.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            background-color: #10a37f;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #0d8c6d;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .description {
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Loading Popup Test</h1>
        <p>This page tests the new loading popup functionality for the AI Prompt Optimizer extension.</p>
        
        <div class="test-section">
            <h3>Test Different Loading Types</h3>
            <p class="description">Click the buttons below to test different loading popup types:</p>
            
            <button class="test-button" onclick="testLoadingPopup('optimizePrompt')">
                Test Optimize Prompt Loading
            </button>
            
            <button class="test-button" onclick="testLoadingPopup('question_optimize')">
                Test Question Optimize Loading
            </button>
            
            <button class="test-button" onclick="testLoadingPopup('prompt_detail_form')">
                Test Instruction Optimize Loading
            </button>
            
            <button class="test-button" onclick="testLoadingPopup('getRelatedQuestionsTopics')">
                Test Related Questions Loading
            </button>
            
            <button class="test-button" onclick="testLoadingPopup('getCriticalAnalysis')">
                Test Critical Analysis Loading
            </button>
            
            <button class="test-button" onclick="testLoadingPopup('default')">
                Test Default Loading
            </button>
        </div>
        
        <div class="test-section">
            <h3>Test Auto-Hide Loading</h3>
            <p class="description">This button will show a loading popup and automatically hide it after 3 seconds:</p>
            
            <button class="test-button" onclick="testAutoHideLoading()">
                Test Auto-Hide Loading (3s)
            </button>
        </div>
        
        <div class="test-section">
            <h3>Manual Controls</h3>
            <p class="description">Manually control the loading popup:</p>
            
            <button class="test-button" onclick="hideLoadingPopup()">
                Hide Current Loading Popup
            </button>
        </div>
    </div>

    <script>
        // Mock Chrome extension APIs
        window.chrome = {
            runtime: {
                getURL: (path) => path
            },
            i18n: {
                getMessage: (key) => {
                    // Mock localized strings
                    const messages = {
                        'optimizingPromptTitle': 'Optimizing Prompt',
                        'optimizingPromptMessage': 'Please wait while we optimize your prompt...',
                        'optimizingQuestionTitle': 'Optimizing Question',
                        'optimizingQuestionMessage': 'Please wait while we optimize your question...',
                        'optimizingInstructionTitle': 'Optimizing Instructions',
                        'optimizingInstructionMessage': 'Please wait while we optimize your instructions...',
                        'gettingRelatedTitle': 'Getting Related Content',
                        'gettingRelatedMessage': 'Please wait while we find related questions and topics...',
                        'analyzingTitle': 'Analyzing Content',
                        'analyzingMessage': 'Please wait while we perform critical analysis...',
                        'processingTitle': 'Processing',
                        'processingMessage': 'Please wait while we process your request...'
                    };
                    return messages[key] || key;
                }
            }
        };

        // Mock getLocalizedString function
        function getLocalizedString(key) {
            return chrome.i18n.getMessage(key);
        }

        // Test function to show loading popup
        function testLoadingPopup(operationType) {
            console.log('Testing loading popup for:', operationType);
            showLoadingPopup(operationType);
        }

        // Test function to show loading popup and auto-hide
        function testAutoHideLoading() {
            console.log('Testing auto-hide loading popup');
            showLoadingPopup('optimizePrompt');
            setTimeout(() => {
                hideLoadingPopup();
            }, 3000);
        }
    </script>
    
    <!-- Include the content script functions -->
    <script type="module">
        // Import and define the functions we need for testing
        
        // Create popup function (simplified version for testing)
        function createPopup(options) {
            // Remove existing popups
            const existingPopups = document.querySelectorAll('.ai-prompt-optimizer-popup');
            existingPopups.forEach(popup => popup.remove());
            
            const popup = document.createElement('div');
            popup.id = options.id || 'ai-popup';
            popup.className = `ai-prompt-optimizer-popup ${options.type || ''}`;
            
            const popupContent = document.createElement('div');
            popupContent.className = 'ai-prompt-optimizer-popup-content';
            
            const popupHeader = document.createElement('div');
            popupHeader.className = 'ai-prompt-optimizer-popup-header';
            popupHeader.textContent = options.title || '';
            
            const popupBody = document.createElement('div');
            popupBody.className = 'ai-prompt-optimizer-popup-body';
            
            if (typeof options.contentGenerator === 'function') {
                options.contentGenerator(popupBody, popup);
            }
            
            popupContent.appendChild(popupHeader);
            popupContent.appendChild(popupBody);
            popup.appendChild(popupContent);
            
            return popup;
        }

        // Create loading popup function
        function createLoadingPopup(operationType) {
            let titleText = '';
            let messageText = '';
            
            switch (operationType) {
                case 'optimizePrompt':
                    titleText = getLocalizedString('optimizingPromptTitle');
                    messageText = getLocalizedString('optimizingPromptMessage');
                    break;
                case 'question_optimize':
                    titleText = getLocalizedString('optimizingQuestionTitle');
                    messageText = getLocalizedString('optimizingQuestionMessage');
                    break;
                case 'prompt_detail_form':
                    titleText = getLocalizedString('optimizingInstructionTitle');
                    messageText = getLocalizedString('optimizingInstructionMessage');
                    break;
                case 'getRelatedQuestionsTopics':
                    titleText = getLocalizedString('gettingRelatedTitle');
                    messageText = getLocalizedString('gettingRelatedMessage');
                    break;
                case 'getCriticalAnalysis':
                    titleText = getLocalizedString('analyzingTitle');
                    messageText = getLocalizedString('analyzingMessage');
                    break;
                default:
                    titleText = getLocalizedString('processingTitle');
                    messageText = getLocalizedString('processingMessage');
                    break;
            }

            return createPopup({
                type: 'loading',
                id: 'ai-loading-popup',
                title: titleText,
                contentGenerator: (popupBody) => {
                    const loadingContainer = document.createElement('div');
                    loadingContainer.className = 'ai-loading-popup-container';

                    const loadingSpinner = document.createElement('div');
                    loadingSpinner.className = 'ai-loading-popup-spinner';
                    
                    const loadingMessage = document.createElement('div');
                    loadingMessage.className = 'ai-loading-popup-message';
                    loadingMessage.textContent = messageText;

                    loadingContainer.appendChild(loadingSpinner);
                    loadingContainer.appendChild(loadingMessage);
                    popupBody.appendChild(loadingContainer);
                },
                buttons: []
            });
        }

        // Position popup function
        function positionPopup(popup) {
            popup.style.position = 'fixed';
            popup.style.top = '50%';
            popup.style.left = '50%';
            popup.style.transform = 'translate(-50%, -50%)';
            popup.style.zIndex = '10000';
        }

        // Show loading popup function
        window.showLoadingPopup = function(operationType) {
            const existingLoadingPopup = document.getElementById('ai-loading-popup');
            if (existingLoadingPopup) {
                existingLoadingPopup.remove();
            }

            const loadingPopup = createLoadingPopup(operationType);
            document.body.appendChild(loadingPopup);
            positionPopup(loadingPopup);
            
            // Show with animation
            setTimeout(() => {
                loadingPopup.style.opacity = '1';
            }, 50);
        };

        // Hide loading popup function
        window.hideLoadingPopup = function() {
            const loadingPopup = document.getElementById('ai-loading-popup');
            if (loadingPopup) {
                loadingPopup.style.opacity = '0';
                setTimeout(() => {
                    loadingPopup.remove();
                }, 300);
            }
        };
    </script>
</body>
</html>
