// Test script for invalid JSON handling in the AI Prompt Optimizer extension

// Mock the chrome API
const chrome = {
  runtime: {
    sendMessage: (message, callback) => {
      console.log('Sending message:', message);
      
      // Simulate different responses
      if (message.action === 'optimizePrompt') {
        // Test case 1: Valid JSON response
        if (message.prompt.includes('test-valid-json')) {
          callback({
            success: true,
            optimizedPrompt: JSON.stringify({
              generated: {
                prompt: "This is a valid optimized prompt",
                requirements: "These are the requirements"
              }
            })
          });
        }
        // Test case 2: Invalid JSON response (not parseable)
        else if (message.prompt.includes('test-invalid-json')) {
          callback({
            success: true,
            optimizedPrompt: "This is not valid JSON { broken: json"
          });
        }
        // Test case 3: Valid J<PERSON> but missing required fields
        else if (message.prompt.includes('test-missing-fields')) {
          callback({
            success: true,
            optimizedPrompt: JSON.stringify({
              generated: {
                // Missing prompt and requirements fields
                other: "Some other data"
              }
            })
          });
        }
        // Test case 4: Valid <PERSON> but missing generated object
        else if (message.prompt.includes('test-missing-generated')) {
          callback({
            success: true,
            optimizedPrompt: JSON.stringify({
              // Missing generated object
              other: "Some other data"
            })
          });
        }
        // Success case
        else {
          callback({
            success: true,
            optimizedPrompt: JSON.stringify({
              generated: {
                prompt: "This is an optimized prompt",
                requirements: "These are the requirements"
              }
            })
          });
        }
      }
    },
    getURL: (path) => {
      return `chrome-extension://fake-extension-id/${path}`;
    }
  },
  i18n: {
    getMessage: (key) => {
      // Mock localized strings
      const messages = {
        'errorTitle': 'Error',
        'closeButtonText': 'Close',
        'errorAIGenerationFailed': 'AI generation failed. Please try again.',
        'errorLoginRequired': 'Login is required to use this feature.',
        'errorExceedFreeTrialQuota': 'You have exceeded your free trial quota.',
        'errorExceedDailyQuota': 'You have exceeded your daily quota.',
        'errorExceedMsgLimit': 'You have exceeded the message limit.',
        'errorServiceUnavailable': 'Service is currently unavailable. Please try again later.',
        'pricingButton': 'View Plans',
        'loginFunBlocksButton': 'Login FunBlocks AI Account'
      };
      return messages[key] || key;
    }
  }
};

// Mock document functions
const document = {
  createElement: (tag) => {
    console.log(`Creating element: ${tag}`);
    return {
      id: '',
      className: '',
      style: {},
      appendChild: (child) => {
        console.log('Appending child:', child);
      },
      addEventListener: (event, handler) => {
        console.log(`Adding event listener for ${event}`);
      },
      setAttribute: (attr, value) => {
        console.log(`Setting attribute ${attr}=${value}`);
      },
      textContent: ''
    };
  },
  getElementById: (id) => {
    console.log(`Getting element by ID: ${id}`);
    return {
      classList: {
        add: (className) => {
          console.log(`Adding class ${className} to element with ID ${id}`);
        },
        remove: (className) => {
          console.log(`Removing class ${className} from element with ID ${id}`);
        }
      },
      disabled: false
    };
  },
  body: {
    appendChild: (child) => {
      console.log('Appending child to body:', child);
    }
  },
  querySelectorAll: (selector) => {
    console.log(`Querying selector: ${selector}`);
    return [];
  }
};

// Mock extractJSONFromString function
function extractJSONFromString(str) {
  console.log('Extracting JSON from string:', str);
  
  try {
    // Simple JSON parsing for testing
    if (str.includes('{') && str.includes('}')) {
      // For test-invalid-json case
      if (str === "This is not valid JSON { broken: json") {
        console.log('Invalid JSON detected');
        return null;
      }
      
      // For test-missing-fields case
      if (str.includes('"other":"Some other data"') && str.includes('"generated"')) {
        console.log('Missing fields detected');
        return {
          generated: {
            // Missing prompt and requirements fields
            other: "Some other data"
          }
        };
      }
      
      // For test-missing-generated case
      if (str.includes('"other":"Some other data"') && !str.includes('"generated"')) {
        console.log('Missing generated object detected');
        return {
          // Missing generated object
          other: "Some other data"
        };
      }
      
      // Normal case
      return JSON.parse(str);
    }
    return null;
  } catch (error) {
    console.error('Error parsing JSON:', error);
    return null;
  }
}

// Test the JSON handling
console.log('=== Testing Valid JSON ===');
handleOptimizeClick('test-valid-json');

console.log('\n=== Testing Invalid JSON ===');
handleOptimizeClick('test-invalid-json');

console.log('\n=== Testing Missing Fields ===');
handleOptimizeClick('test-missing-fields');

console.log('\n=== Testing Missing Generated Object ===');
handleOptimizeClick('test-missing-generated');

// Mock the handleOptimizeClick function
function handleOptimizeClick(testInput) {
  const platform = 'chatgpt.com';
  const inputText = testInput || 'test input';
  
  console.log('Input text:', inputText);
  
  // Update button state
  const button = document.getElementById('ai-prompt-optimizer-button');
  button.classList.add('loading');
  button.disabled = true;
  
  // Send message to background script
  chrome.runtime.sendMessage(
    { action: 'optimizePrompt', prompt: inputText },
    (response) => {
      // Reset button state
      button.classList.remove('loading');
      button.disabled = false;
      
      if (response && response.success && response.optimizedPrompt) {
        console.log('Optimized prompt received:', response.optimizedPrompt);
        
        const jsonResult = extractJSONFromString(response.optimizedPrompt);
        
        // Check if the extracted JSON is valid and has the expected structure
        if (!jsonResult || !jsonResult.generated || (!jsonResult.generated.prompt && !jsonResult.generated.requirements)) {
          console.error('Failed to extract valid JSON from response:', response.optimizedPrompt);
          
          // Show error message in popup
          const errorMessage = chrome.i18n.getMessage('errorAIGenerationFailed');
          console.log('Creating error popup for invalid JSON:', errorMessage);
          
          // Mock createErrorPopup function
          const errorPopup = createErrorPopup(errorMessage);
          console.log('Error popup created:', errorPopup);
          
          console.log('Adding popup to body');
          document.body.appendChild(errorPopup);
          
          console.log('Positioning popup');
          // Mock positionPopup function
          
          console.log('Showing popup with overlay');
          // Mock showPopupWithOverlay function
          return;
        }
        
        // Create and show popup with optimized prompt data
        console.log('Valid JSON extracted:', jsonResult);
        console.log('Creating optimized prompt popup');
        // Mock createOptimizedPromptPopup function
      } else {
        console.error('Failed to optimize prompt:', response?.error || 'Unknown error');
        
        // Show error message in popup
        const errorMessage = response?.error || 'Unknown error';
        console.log('Creating error popup for:', errorMessage);
      }
    }
  );
}

// Mock the createErrorPopup function
function createErrorPopup(errorMessage) {
  console.log('Creating error popup with message:', errorMessage);
  
  // Return a mock popup object
  return {
    id: 'ai-error-popup',
    className: 'ai-prompt-optimizer-popup ai-error-popup',
    style: {}
  };
}
