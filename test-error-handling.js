// Test script for error handling in the AI Prompt Optimizer extension

// Mock the chrome API
const chrome = {
  runtime: {
    sendMessage: (message, callback) => {
      console.log('Sending message:', message);
      
      // Simulate different error responses
      if (message.action === 'optimizePrompt') {
        // Test case 1: Login required (403)
        if (message.prompt.includes('test-login-error')) {
          callback({
            success: false,
            error: 'Login required'
          });
        }
        // Test case 2: Quota exceeded
        else if (message.prompt.includes('test-quota-error')) {
          callback({
            success: false,
            error: 'exceed_free_trial_quota'
          });
        }
        // Test case 3: Daily quota exceeded
        else if (message.prompt.includes('test-daily-quota')) {
          callback({
            success: false,
            error: 'exceed_daily_quota'
          });
        }
        // Test case 4: Message limit exceeded
        else if (message.prompt.includes('test-msg-limit')) {
          callback({
            success: false,
            error: 'exceed_msg_limit'
          });
        }
        // Test case 5: Generic error
        else if (message.prompt.includes('test-generic-error')) {
          callback({
            success: false,
            error: 'Failed to generate card'
          });
        }
        // Success case
        else {
          callback({
            success: true,
            optimizedPrompt: 'This is an optimized prompt'
          });
        }
      }
    },
    getURL: (path) => {
      return `chrome-extension://fake-extension-id/${path}`;
    }
  },
  i18n: {
    getMessage: (key) => {
      // Mock localized strings
      const messages = {
        'errorTitle': 'Error',
        'closeButtonText': 'Close',
        'errorLoginRequired': 'Login is required to use this feature.',
        'errorExceedFreeTrialQuota': 'You have exceeded your free trial quota.',
        'errorExceedDailyQuota': 'You have exceeded your daily quota.',
        'errorExceedMsgLimit': 'You have exceeded the message limit.',
        'errorServiceUnavailable': 'Service is currently unavailable. Please try again later.',
        'pricingButton': 'View Plans',
        'loginFunBlocksButton': 'Login FunBlocks AI Account'
      };
      return messages[key] || key;
    }
  }
};

// Mock document functions
const document = {
  createElement: (tag) => {
    console.log(`Creating element: ${tag}`);
    return {
      id: '',
      className: '',
      style: {},
      appendChild: (child) => {
        console.log('Appending child:', child);
      },
      addEventListener: (event, handler) => {
        console.log(`Adding event listener for ${event}`);
      },
      setAttribute: (attr, value) => {
        console.log(`Setting attribute ${attr}=${value}`);
      },
      textContent: ''
    };
  },
  getElementById: (id) => {
    console.log(`Getting element by ID: ${id}`);
    return {
      classList: {
        add: (className) => {
          console.log(`Adding class ${className} to element with ID ${id}`);
        },
        remove: (className) => {
          console.log(`Removing class ${className} from element with ID ${id}`);
        }
      },
      disabled: false
    };
  },
  body: {
    appendChild: (child) => {
      console.log('Appending child to body:', child);
    }
  },
  querySelectorAll: (selector) => {
    console.log(`Querying selector: ${selector}`);
    return [];
  }
};

// Mock window functions
const window = {
  open: (url) => {
    console.log(`Opening URL: ${url}`);
  }
};

// Test the error handling
console.log('=== Testing Login Error ===');
handleOptimizeClick('test-login-error');

console.log('\n=== Testing Free Trial Quota Error ===');
handleOptimizeClick('test-quota-error');

console.log('\n=== Testing Daily Quota Error ===');
handleOptimizeClick('test-daily-quota');

console.log('\n=== Testing Message Limit Error ===');
handleOptimizeClick('test-msg-limit');

console.log('\n=== Testing Generic Error ===');
handleOptimizeClick('test-generic-error');

// Mock the handleOptimizeClick function
function handleOptimizeClick(testInput) {
  const inputText = testInput || 'test input';
  
  console.log('Input text:', inputText);
  
  // Update button state
  const button = document.getElementById('ai-prompt-optimizer-button');
  button.classList.add('loading');
  button.disabled = true;
  
  // Send message to background script
  chrome.runtime.sendMessage(
    { action: 'optimizePrompt', prompt: inputText },
    (response) => {
      // Reset button state
      button.classList.remove('loading');
      button.disabled = false;
      
      if (response && response.success && response.optimizedPrompt) {
        console.log('Optimized prompt:', response.optimizedPrompt);
        console.log('Creating and showing optimized prompt popup');
      } else {
        console.error('Failed to optimize prompt:', response?.error || 'Unknown error');
        
        // Show error message in popup
        const errorMessage = response?.error || 'Unknown error';
        console.log('Creating error popup for:', errorMessage);
        
        // Mock the createErrorPopup function
        const errorPopup = createErrorPopup(errorMessage);
        console.log('Error popup created:', errorPopup);
        
        console.log('Adding popup to body');
        document.body.appendChild(errorPopup);
        
        console.log('Positioning popup');
        // Mock positionPopup function
        
        console.log('Showing popup with overlay');
        // Mock showPopupWithOverlay function
      }
    }
  );
}

// Mock the createErrorPopup function
function createErrorPopup(errorMessage) {
  // Format error message
  let formattedErrorMessage = errorMessage;
  let buttons = [];
  
  // Handle specific error codes
  if (['exceed_free_trial_quota', 'exceed_daily_quota', 'exceed_msg_limit'].includes(errorMessage)) {
    if (errorMessage === 'exceed_free_trial_quota') {
      formattedErrorMessage = chrome.i18n.getMessage('errorExceedFreeTrialQuota');
    } else if (errorMessage === 'exceed_daily_quota') {
      formattedErrorMessage = chrome.i18n.getMessage('errorExceedDailyQuota');
    } else if (errorMessage === 'exceed_msg_limit') {
      formattedErrorMessage = chrome.i18n.getMessage('errorExceedMsgLimit');
    }
    
    // Add pricing button for quota errors
    buttons = [
      {
        type: 'confirm',
        text: chrome.i18n.getMessage('pricingButton'),
        onClick: (_, popup) => {
          openPricingPage();
          const overlay = document.getElementById('ai-prompt-optimizer-overlay');
          closePopup(popup, overlay);
        }
      },
      {
        type: 'cancel',
        text: chrome.i18n.getMessage('closeButtonText'),
        onClick: (_, popup) => {
          const overlay = document.getElementById('ai-prompt-optimizer-overlay');
          closePopup(popup, overlay);
        }
      }
    ];
    
    console.log('Adding pricing button for quota error');
  } else if (errorMessage === 'Login required' || errorMessage.includes('403')) {
    formattedErrorMessage = chrome.i18n.getMessage('errorLoginRequired');
    
    // Add login button for login errors
    buttons = [
      {
        type: 'confirm',
        text: chrome.i18n.getMessage('loginFunBlocksButton'),
        onClick: (_, popup) => {
          openLoginPage();
          const overlay = document.getElementById('ai-prompt-optimizer-overlay');
          closePopup(popup, overlay);
        }
      },
      {
        type: 'cancel',
        text: chrome.i18n.getMessage('closeButtonText'),
        onClick: (_, popup) => {
          const overlay = document.getElementById('ai-prompt-optimizer-overlay');
          closePopup(popup, overlay);
        }
      }
    ];
    
    console.log('Adding login button for login error');
  } else if (errorMessage === 'Failed to request optimization service' || errorMessage === 'Failed to generate card') {
    formattedErrorMessage = chrome.i18n.getMessage('errorServiceUnavailable');
    
    // Default close button for other errors
    buttons = [
      {
        type: 'cancel',
        text: chrome.i18n.getMessage('closeButtonText'),
        onClick: (_, popup) => {
          const overlay = document.getElementById('ai-prompt-optimizer-overlay');
          closePopup(popup, overlay);
        }
      }
    ];
    
    console.log('Adding close button for service error');
  } else {
    // Default close button for other errors
    buttons = [
      {
        type: 'cancel',
        text: chrome.i18n.getMessage('closeButtonText'),
        onClick: (_, popup) => {
          const overlay = document.getElementById('ai-prompt-optimizer-overlay');
          closePopup(popup, overlay);
        }
      }
    ];
    
    console.log('Adding close button for generic error');
  }
  
  console.log('Formatted error message:', formattedErrorMessage);
  console.log('Buttons:', buttons.map(b => b.text).join(', '));
  
  // Return a mock popup object
  return {
    id: 'ai-error-popup',
    className: 'ai-prompt-optimizer-popup ai-error-popup',
    style: {}
  };
}

// Mock helper functions
function openLoginPage() {
  const funblocks_domain = 'funblocks.net';
  window.open(`https://app.${funblocks_domain}/#/login?source=extension`);
}

function openPricingPage() {
  window.open('https://app.funblocks.net/#/aiplans', '_blank');
}
