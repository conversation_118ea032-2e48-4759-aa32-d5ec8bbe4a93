#!/bin/bash

# 确保安装了必要的工具
# 需要安装 Inkscape 或 librsvg (rsvg-convert)
# 例如：brew install librsvg 或 apt-get install librsvg2-bin

# 创建不同尺寸的图标
echo "Converting main icon to different sizes..."

# 使用 rsvg-convert (如果有)
if command -v rsvg-convert &> /dev/null; then
    rsvg-convert -w 16 -h 16 src/images/icon.svg -o src/images/icon16.png
    rsvg-convert -w 48 -h 48 src/images/icon.svg -o src/images/icon48.png
    rsvg-convert -w 128 -h 128 src/images/icon.svg -o src/images/icon128.png
    rsvg-convert -w 24 -h 24 src/images/button-icon.svg -o src/images/button-icon.png
# 使用 Inkscape (如果有)
elif command -v inkscape &> /dev/null; then
    inkscape src/images/icon.svg --export-width=16 --export-height=16 --export-filename=src/images/icon16.png
    inkscape src/images/icon.svg --export-width=48 --export-height=48 --export-filename=src/images/icon48.png
    inkscape src/images/icon.svg --export-width=128 --export-height=128 --export-filename=src/images/icon128.png
    inkscape src/images/button-icon.svg --export-width=24 --export-height=24 --export-filename=src/images/button-icon.png
else
    echo "Error: Neither rsvg-convert nor Inkscape found. Please install one of them."
    exit 1
fi

echo "Icon conversion complete!"
