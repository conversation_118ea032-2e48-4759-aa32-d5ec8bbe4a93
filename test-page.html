<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page for Chrome Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .message {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .user-message {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        .ai-message {
            background: #f3e5f5;
            border-color: #9c27b0;
        }
        .message-actions {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }
        .test-button {
            padding: 5px 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        .test-button:hover {
            background: #f0f0f0;
        }
        .input-area {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #4caf50;
            border-radius: 8px;
            background: #f1f8e9;
        }
        #prompt-textarea {
            width: 100%;
            min-height: 100px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: inherit;
            resize: vertical;
        }
        .composer-footer {
            margin-top: 10px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
    </style>
</head>
<body>
    <h1>Chrome Extension Test Page</h1>
    <p>This page simulates a chat interface to test the Chrome extension functionality.</p>

    <div style="background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 10px 0;">
        <strong>Test Instructions:</strong>
        <ul>
            <li>Scroll to different parts of the page to test dropdown positioning</li>
            <li>Try clicking the related questions & topics buttons near the top and bottom of the page</li>
            <li><strong>Default behavior:</strong> Dropdown appears <em>above</em> the button</li>
            <li><strong>Fallback behavior:</strong> Only appears <em>below</em> when there's insufficient space above</li>
        </ul>
    </div>

    <!-- Input area (simulates ChatGPT input) -->
    <div class="input-area">
        <h3>Chat Input Area</h3>
        <textarea id="prompt-textarea" placeholder="Type your message here..."></textarea>
        <div class="composer-footer" data-testid="composer-footer-actions">
            <button class="test-button">Send</button>
        </div>
    </div>

    <!-- User message -->
    <div class="message user-message" data-message-id="user-1">
        <h4>User Question:</h4>
        <p>What are the best practices for machine learning model deployment?</p>
        <div class="message-actions">
            <button class="test-button" data-testid="copy-turn-action-button">Copy</button>
        </div>
    </div>

    <!-- AI response -->
    <div class="message ai-message" data-message-id="ai-1">
        <h4>AI Response:</h4>
        <p>Machine learning model deployment involves several best practices to ensure reliable, scalable, and maintainable systems. Here are the key considerations:</p>
        
        <h5>1. Model Versioning and Management</h5>
        <ul>
            <li>Use version control for both code and models</li>
            <li>Implement model registries to track different versions</li>
            <li>Maintain clear documentation for each model version</li>
        </ul>

        <h5>2. Infrastructure and Scalability</h5>
        <ul>
            <li>Choose appropriate deployment platforms (cloud, on-premise, edge)</li>
            <li>Implement auto-scaling based on demand</li>
            <li>Use containerization (Docker) for consistent environments</li>
        </ul>

        <h5>3. Monitoring and Observability</h5>
        <ul>
            <li>Monitor model performance metrics</li>
            <li>Track data drift and model drift</li>
            <li>Set up alerting for anomalies</li>
        </ul>

        <div class="message-actions">
            <button class="test-button" data-testid="copy-turn-action-button">Copy</button>
        </div>
    </div>

    <!-- Another user message -->
    <div class="message user-message" data-message-id="user-2">
        <h4>User Question:</h4>
        <p>How do I handle data drift in production ML models?</p>
        <div class="message-actions">
            <button class="test-button" data-testid="copy-turn-action-button">Copy</button>
        </div>
    </div>

    <!-- Add some spacing to test different screen positions -->
    <div style="height: 300px; background: linear-gradient(to bottom, #f0f0f0, #e0e0e0); display: flex; align-items: center; justify-content: center; margin: 20px 0; border-radius: 8px;">
        <p style="text-align: center; color: #666;">
            <strong>Scroll Test Area</strong><br>
            This area helps test dropdown positioning at different scroll positions
        </p>
    </div>

    <!-- Message near bottom of page -->
    <div class="message ai-message" data-message-id="ai-2">
        <h4>AI Response (Near Bottom):</h4>
        <p>Data drift detection is crucial for maintaining model performance in production. Here are the key strategies:</p>

        <h5>Statistical Methods</h5>
        <ul>
            <li>Kolmogorov-Smirnov test for distribution changes</li>
            <li>Population Stability Index (PSI) monitoring</li>
            <li>Jensen-Shannon divergence for feature drift</li>
        </ul>

        <h5>Monitoring Approaches</h5>
        <ul>
            <li>Real-time drift detection systems</li>
            <li>Automated alerting mechanisms</li>
            <li>Dashboard visualization of drift metrics</li>
        </ul>

        <div class="message-actions">
            <button class="test-button" data-testid="copy-turn-action-button">Copy</button>
        </div>
    </div>

    <!-- Final user message at the very bottom -->
    <div class="message user-message" data-message-id="user-3">
        <h4>User Question (Bottom of Page):</h4>
        <p>What are the best practices for automated model retraining when drift is detected?</p>
        <div class="message-actions">
            <button class="test-button" data-testid="copy-turn-action-button">Copy</button>
        </div>
    </div>

    <div style="height: 100px; background: #f9f9f9; display: flex; align-items: center; justify-content: center; margin-top: 20px; border-radius: 8px;">
        <p style="color: #999;">End of test page - dropdown should appear above buttons (default behavior)</p>
    </div>

    <script>
        // Simulate some basic interactions
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded. Chrome extension should inject buttons here.');

            // Add some basic event listeners for testing
            document.querySelectorAll('.test-button').forEach(button => {
                button.addEventListener('click', function(e) {
                    console.log('Test button clicked:', e.target.textContent);
                });
            });

            // Simulate a long markdown content for testing Critical Analysis
            window.testMarkdownContent = `# Critical Analysis of Machine Learning Model Deployment

## Executive Summary

This analysis examines the current state of machine learning model deployment practices and identifies key areas for improvement. The deployment phase is often the most challenging aspect of the ML lifecycle, requiring careful consideration of multiple factors.

## Key Findings

### 1. Infrastructure Challenges

**Current State:**
- Many organizations struggle with scalability issues
- Infrastructure costs can spiral out of control
- Monitoring and observability are often afterthoughts

**Recommendations:**
- Implement auto-scaling mechanisms
- Use containerization for consistency
- Establish comprehensive monitoring from day one

### 2. Model Performance in Production

The gap between development and production performance is a critical concern:

\`\`\`python
# Example monitoring code
def monitor_model_performance(model, data):
    predictions = model.predict(data)
    accuracy = calculate_accuracy(predictions, ground_truth)

    if accuracy < THRESHOLD:
        alert_team("Model performance degraded")
        trigger_retraining()
\`\`\`

### 3. Data Drift Detection

> "Data drift is the silent killer of ML models in production" - Industry Expert

Key indicators to monitor:
- **Statistical drift**: Changes in data distribution
- **Concept drift**: Changes in the relationship between features and target
- **Covariate shift**: Changes in input feature distributions

## Detailed Analysis

### Security Considerations

1. **Model Security**
   - Protect against adversarial attacks
   - Implement input validation
   - Use secure communication protocols

2. **Data Privacy**
   - Ensure GDPR compliance
   - Implement data anonymization
   - Regular security audits

### Performance Optimization

| Metric | Target | Current | Gap |
|--------|--------|---------|-----|
| Latency | <100ms | 150ms | -50ms |
| Throughput | 1000 RPS | 750 RPS | -250 RPS |
| Accuracy | >95% | 92% | -3% |

### Cost Analysis

The total cost of ownership includes:

- **Infrastructure costs**: 40%
- **Development time**: 30%
- **Maintenance**: 20%
- **Monitoring tools**: 10%

## Recommendations

### Short-term (1-3 months)
1. Implement basic monitoring
2. Set up automated alerts
3. Establish performance baselines

### Medium-term (3-6 months)
1. Deploy A/B testing framework
2. Implement automated retraining
3. Enhance security measures

### Long-term (6-12 months)
1. Build MLOps pipeline
2. Implement advanced monitoring
3. Establish governance framework

## Conclusion

Successful ML model deployment requires a holistic approach that considers technical, operational, and business factors. Organizations that invest in proper deployment infrastructure and processes will see significantly better outcomes.

---

*This analysis is based on industry best practices and real-world deployment experiences across multiple organizations.*`;
        });
    </script>
</body>
</html>
