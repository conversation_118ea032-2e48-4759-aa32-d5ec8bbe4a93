// Test script for refactored error handling in the AI Prompt Optimizer extension

// Mock the chrome API
const chrome = {
  runtime: {
    sendMessage: (message, callback) => {
      console.log('Sending message:', message);
      
      // Simulate different responses
      if (message.action === 'optimizePrompt') {
        // Test case 1: Valid JSON response
        if (message.prompt.includes('test-valid-json')) {
          callback({
            success: true,
            optimizedPrompt: JSON.stringify({
              generated: {
                prompt: "This is a valid optimized prompt",
                requirements: "These are the requirements"
              }
            })
          });
        }
        // Test case 2: Invalid JSON response
        else if (message.prompt.includes('test-invalid-json')) {
          callback({
            success: true,
            optimizedPrompt: "This is not valid JSON { broken: json"
          });
        }
        // Test case 3: API error
        else if (message.prompt.includes('test-api-error')) {
          callback({
            success: false,
            error: 'Login required'
          });
        }
        // Default case
        else {
          callback({
            success: true,
            optimizedPrompt: JSON.stringify({
              generated: {
                prompt: "Default optimized prompt",
                requirements: "Default requirements"
              }
            })
          });
        }
      }
    },
    getURL: (path) => {
      return `chrome-extension://fake-extension-id/${path}`;
    }
  },
  i18n: {
    getMessage: (key) => {
      // Mock localized strings
      const messages = {
        'errorTitle': 'Error',
        'closeButtonText': 'Close',
        'errorAIGenerationFailed': 'AI generation failed. Please try again.',
        'errorLoginRequired': 'Login is required to use this feature.',
        'errorExceedFreeTrialQuota': 'You have exceeded your free trial quota.',
        'errorExceedDailyQuota': 'You have exceeded your daily quota.',
        'errorExceedMsgLimit': 'You have exceeded the message limit.',
        'errorServiceUnavailable': 'Service is currently unavailable. Please try again later.',
        'pricingButton': 'View Plans',
        'loginFunBlocksButton': 'Login FunBlocks AI Account'
      };
      return messages[key] || key;
    }
  }
};

// Mock document functions
const document = {
  createElement: (tag) => {
    console.log(`Creating element: ${tag}`);
    return {
      id: '',
      className: '',
      style: {},
      appendChild: (child) => {
        console.log('Appending child:', child);
      },
      addEventListener: (event, handler) => {
        console.log(`Adding event listener for ${event}`);
      },
      setAttribute: (attr, value) => {
        console.log(`Setting attribute ${attr}=${value}`);
      },
      textContent: ''
    };
  },
  getElementById: (id) => {
    console.log(`Getting element by ID: ${id}`);
    return {
      classList: {
        add: (className) => {
          console.log(`Adding class ${className} to element with ID ${id}`);
        },
        remove: (className) => {
          console.log(`Removing class ${className} from element with ID ${id}`);
        }
      },
      disabled: false
    };
  },
  body: {
    appendChild: (child) => {
      console.log('Appending child to body:', child);
    }
  },
  querySelectorAll: (selector) => {
    console.log(`Querying selector: ${selector}`);
    return [];
  }
};

// Mock window functions
const window = {
  open: (url) => {
    console.log(`Opening URL: ${url}`);
  }
};

// Mock extractJSONFromString function
function extractJSONFromString(str) {
  console.log('Extracting JSON from string:', str);
  
  try {
    // Simple JSON parsing for testing
    if (str.includes('{') && str.includes('}')) {
      // For test-invalid-json case
      if (str === "This is not valid JSON { broken: json") {
        console.log('Invalid JSON detected');
        return null;
      }
      
      // Normal case
      return JSON.parse(str);
    }
    return null;
  } catch (error) {
    console.error('Error parsing JSON:', error);
    return null;
  }
}

// Mock createErrorPopup function
function createErrorPopup(errorMessage) {
  console.log('Creating error popup with message:', errorMessage);
  
  // Return a mock popup object
  return {
    id: 'ai-error-popup',
    className: 'ai-prompt-optimizer-popup ai-error-popup',
    style: {}
  };
}

// Mock positionPopup function
function positionPopup(popup) {
  console.log('Positioning popup:', popup);
}

// Mock showPopupWithOverlay function
function showPopupWithOverlay(popup) {
  console.log('Showing popup with overlay:', popup);
}

// Mock createOptimizedPromptPopup function
function createOptimizedPromptPopup(data, platform) {
  console.log('Creating optimized prompt popup with data:', data);
  console.log('Platform:', platform);
  
  // Return a mock popup object
  return {
    id: 'ai-optimized-prompt-popup',
    className: 'ai-prompt-optimizer-popup',
    style: {}
  };
}

// Mock showErrorPopup function
function showErrorPopup(errorMessage) {
  console.log('Showing error popup with message:', errorMessage);
  const errorPopup = createErrorPopup(errorMessage);
  document.body.appendChild(errorPopup);
  positionPopup(errorPopup);
  showPopupWithOverlay(errorPopup);
  return errorPopup;
}

// Test the refactored error handling
console.log('=== Testing Valid JSON ===');
handleOptimizeClick('test-valid-json');

console.log('\n=== Testing Invalid JSON ===');
handleOptimizeClick('test-invalid-json');

console.log('\n=== Testing API Error ===');
handleOptimizeClick('test-api-error');

// Mock the handleOptimizeClick function
function handleOptimizeClick(testInput) {
  const platform = 'chatgpt.com';
  const inputText = testInput || 'test input';
  
  console.log('Input text:', inputText);
  
  // Update button state
  const button = document.getElementById('ai-prompt-optimizer-button');
  button.classList.add('loading');
  button.disabled = true;
  
  // Send message to background script
  chrome.runtime.sendMessage(
    { action: 'optimizePrompt', prompt: inputText },
    (response) => {
      // Reset button state
      button.classList.remove('loading');
      button.disabled = false;
      
      if (response && response.success && response.optimizedPrompt) {
        console.log('Optimized prompt received:', response.optimizedPrompt);
        
        const jsonResult = extractJSONFromString(response.optimizedPrompt);
        
        // Check if the extracted JSON is valid and has the expected structure
        if (!jsonResult || !jsonResult.generated || (!jsonResult.generated.prompt && !jsonResult.generated.requirements)) {
          console.error('Failed to extract valid JSON from response:', response.optimizedPrompt);
          
          // Show error message for invalid JSON
          showErrorPopup(chrome.i18n.getMessage('errorAIGenerationFailed') || 'AI generation failed. Please try again.');
          return;
        }
        
        // Create and show popup with optimized prompt data
        const popupData = jsonResult;
        const popup = createOptimizedPromptPopup(popupData, platform);
        document.body.appendChild(popup);
        positionPopup(popup);
        showPopupWithOverlay(popup);
      } else {
        console.error('Failed to optimize prompt:', response?.error || 'Unknown error');
        
        // Show error message for API error
        showErrorPopup(response?.error || 'Unknown error');
      }
    }
  );
}
