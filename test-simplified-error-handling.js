// Test script for simplified error handling in the AI Prompt Optimizer extension

// Mock the chrome API
const chrome = {
  runtime: {
    sendMessage: (message, callback) => {
      console.log('Sending message:', message);
      
      // Simulate different error responses
      if (message.action === 'optimizePrompt') {
        // Test case 1: Login required error
        if (message.prompt.includes('test-login-error')) {
          callback({
            success: false,
            errorCode: 'ERR_LOGIN_REQUIRED'
          });
        }
        // Test case 2: Quota exceeded error
        else if (message.prompt.includes('test-quota-error')) {
          callback({
            success: false,
            errorCode: 'ERR_EXCEED_FREE_TRIAL_QUOTA'
          });
        }
        // Test case 3: Service unavailable error
        else if (message.prompt.includes('test-service-error')) {
          callback({
            success: false,
            errorCode: 'ERR_SERVICE_UNAVAILABLE'
          });
        }
        // Test case 4: Invalid JSON error
        else if (message.prompt.includes('test-invalid-json')) {
          callback({
            success: true,
            optimizedPrompt: 'This is not valid JSON'
          });
        }
        // Test case 5: Unknown error code
        else if (message.prompt.includes('test-unknown-error')) {
          callback({
            success: false,
            errorCode: 'UNKNOWN_ERROR_CODE'
          });
        }
        // Success case
        else {
          callback({
            success: true,
            optimizedPrompt: JSON.stringify({
              generated: {
                prompt: "This is an optimized prompt",
                requirements: "These are the requirements"
              }
            })
          });
        }
      }
    },
    getURL: (path) => {
      return `chrome-extension://fake-extension-id/${path}`;
    }
  },
  i18n: {
    getMessage: (key) => {
      // Mock localized strings
      const messages = {
        'errorTitle': 'Error',
        'closeButtonText': 'Close',
        'errorLoginRequired': 'Login is required to use this feature.',
        'errorExceedFreeTrialQuota': 'You have exceeded your free trial quota.',
        'errorExceedDailyQuota': 'You have exceeded your daily quota.',
        'errorExceedMsgLimit': 'You have exceeded the message limit.',
        'errorServiceUnavailable': 'Service is currently unavailable. Please try again later.',
        'errorAIGenerationFailed': 'AI generation failed. Please try again.',
        'errorGeneralError': 'An error occurred. Please try again.',
        'pricingButton': 'View Plans',
        'loginFunBlocksButton': 'Login FunBlocks AI Account'
      };
      return messages[key] || key;
    }
  }
};

// Mock document functions
const document = {
  createElement: (tag) => {
    console.log(`Creating element: ${tag}`);
    return {
      id: '',
      className: '',
      style: {},
      appendChild: (child) => {
        console.log('Appending child:', child);
      },
      addEventListener: (event, handler) => {
        console.log(`Adding event listener for ${event}`);
      },
      setAttribute: (attr, value) => {
        console.log(`Setting attribute ${attr}=${value}`);
      },
      textContent: ''
    };
  },
  getElementById: (id) => {
    console.log(`Getting element by ID: ${id}`);
    return {
      classList: {
        add: (className) => {
          console.log(`Adding class ${className} to element with ID ${id}`);
        },
        remove: (className) => {
          console.log(`Removing class ${className} from element with ID ${id}`);
        }
      },
      disabled: false
    };
  },
  body: {
    appendChild: (child) => {
      console.log('Appending child to body:', child);
    }
  },
  querySelectorAll: (selector) => {
    console.log(`Querying selector: ${selector}`);
    return [];
  }
};

// Mock window functions
const window = {
  open: (url) => {
    console.log(`Opening URL: ${url}`);
  }
};

// Mock helper functions
function getLocalizedString(key) {
  return chrome.i18n.getMessage(key);
}

function openLoginPage() {
  const funblocks_domain = 'funblocks.net';
  window.open(`https://app.${funblocks_domain}/#/login?source=extension`);
}

function openPricingPage() {
  window.open('https://app.funblocks.net/#/aiplans', '_blank');
}

function closePopup(popup, overlay) {
  console.log('Closing popup:', popup);
  console.log('Closing overlay:', overlay);
}

function positionPopup(popup) {
  console.log('Positioning popup:', popup);
}

function showPopupWithOverlay(popup) {
  console.log('Showing popup with overlay:', popup);
}

// Mock extractJSONFromString function
function extractJSONFromString(str) {
  console.log('Extracting JSON from string:', str);
  
  try {
    // Simple JSON parsing for testing
    if (str.includes('{') && str.includes('}')) {
      return JSON.parse(str);
    }
    return null;
  } catch (error) {
    console.error('Error parsing JSON:', error);
    return null;
  }
}

// The simplified createErrorPopup function
function createErrorPopup(errorCode) {
  console.log('Processing error code:', errorCode);
  
  // 根据错误代码获取格式化的错误消息
  let formattedErrorMessage;
  let buttons = [];
  
  // 处理特定错误代码
  switch (errorCode) {
    case 'ERR_EXCEED_FREE_TRIAL_QUOTA':
      formattedErrorMessage = getLocalizedString('errorExceedFreeTrialQuota') || 'You have exceeded your free trial quota.';
      // 添加价格按钮
      buttons = [
        {
          type: 'confirm',
          text: getLocalizedString('pricingButton') || 'View Plans',
          onClick: (_, popup) => {
            openPricingPage();
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
      
    case 'ERR_EXCEED_DAILY_QUOTA':
      formattedErrorMessage = getLocalizedString('errorExceedDailyQuota') || 'You have exceeded your daily quota.';
      // 添加价格按钮
      buttons = [
        {
          type: 'confirm',
          text: getLocalizedString('pricingButton') || 'View Plans',
          onClick: (_, popup) => {
            openPricingPage();
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
      
    case 'ERR_EXCEED_MSG_LIMIT':
      formattedErrorMessage = getLocalizedString('errorExceedMsgLimit') || 'You have exceeded the message limit.';
      // 添加价格按钮
      buttons = [
        {
          type: 'confirm',
          text: getLocalizedString('pricingButton') || 'View Plans',
          onClick: (_, popup) => {
            openPricingPage();
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
      
    case 'ERR_LOGIN_REQUIRED':
      formattedErrorMessage = getLocalizedString('errorLoginRequired') || 'Login is required to use this feature.';
      // 添加登录按钮
      buttons = [
        {
          type: 'confirm',
          text: getLocalizedString('loginFunBlocksButton') || 'Login FunBlocks AI Account',
          onClick: (_, popup) => {
            openLoginPage();
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
      
    case 'ERR_SERVICE_UNAVAILABLE':
      formattedErrorMessage = getLocalizedString('errorServiceUnavailable') || 'Service is currently unavailable. Please try again later.';
      // 默认关闭按钮
      buttons = [
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
      
    case 'ERR_INVALID_JSON':
    case 'ERR_GENERATION_FAILED':
      formattedErrorMessage = getLocalizedString('errorAIGenerationFailed') || 'AI generation failed. Please try again.';
      // 默认关闭按钮
      buttons = [
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
      
    default:
      // 对于未知错误代码，显示通用错误消息
      formattedErrorMessage = getLocalizedString('errorGeneralError') || 'An error occurred. Please try again.';
      // 默认关闭按钮
      buttons = [
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
  }
  
  console.log('Formatted error message:', formattedErrorMessage);
  console.log('Buttons:', buttons.map(b => b.text).join(', '));
  
  return createPopup({
    type: 'error',
    id: 'ai-error-popup',
    title: getLocalizedString('errorTitle') || 'Error',
    contentGenerator: (popupBody) => {
      // 创建错误信息容器
      const errorContainer = document.createElement('div');
      errorContainer.className = 'ai-prompt-optimizer-popup-error-container';

      // 创建错误图标
      const errorIcon = document.createElement('div');
      errorIcon.className = 'ai-prompt-optimizer-popup-error-icon';

      // 创建错误文本
      const errorText = document.createElement('div');
      errorText.className = 'ai-prompt-optimizer-popup-text error-text';
      errorText.textContent = formattedErrorMessage;

      // 将错误图标和文本添加到容器中
      errorContainer.appendChild(errorIcon);
      errorContainer.appendChild(errorText);
      popupBody.appendChild(errorContainer);
    },
    buttons: buttons
  });
}

// Helper function to show error popup
function showErrorPopup(errorCode) {
  const errorPopup = createErrorPopup(errorCode);
  document.body.appendChild(errorPopup);
  positionPopup(errorPopup);
  showPopupWithOverlay(errorPopup);
  return errorPopup;
}

// Mock createPopup function
function createPopup(options) {
  console.log('Creating popup with options:', JSON.stringify({
    type: options.type,
    id: options.id,
    title: options.title,
    buttons: options.buttons.map(b => b.text)
  }, null, 2));
  
  // Call the content generator to test it
  const popupBody = {
    appendChild: (child) => {
      console.log('Appending child to popup body:', child);
    }
  };
  
  if (options.contentGenerator) {
    options.contentGenerator(popupBody);
  }
  
  // Return a mock popup object
  return {
    id: options.id,
    className: `ai-prompt-optimizer-popup ${options.type}-popup`,
    style: {}
  };
}

// Test the simplified error handling
console.log('=== Testing Login Error ===');
showErrorPopup('ERR_LOGIN_REQUIRED');

console.log('\n=== Testing Quota Error ===');
showErrorPopup('ERR_EXCEED_FREE_TRIAL_QUOTA');

console.log('\n=== Testing Service Error ===');
showErrorPopup('ERR_SERVICE_UNAVAILABLE');

console.log('\n=== Testing Invalid JSON Error ===');
showErrorPopup('ERR_INVALID_JSON');

console.log('\n=== Testing Unknown Error Code ===');
showErrorPopup('UNKNOWN_ERROR_CODE');
