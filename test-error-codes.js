// Test script for error code handling in the AI Prompt Optimizer extension

// Mock the chrome API
const chrome = {
  runtime: {
    sendMessage: (message, callback) => {
      console.log('Sending message:', message);
      
      // Simulate different error responses
      if (message.action === 'optimizePrompt') {
        // Test case 1: Login required error
        if (message.prompt.includes('test-login-error')) {
          callback({
            success: false,
            errorCode: 'ERR_LOGIN_REQUIRED'
          });
        }
        // Test case 2: Quota exceeded error
        else if (message.prompt.includes('test-quota-error')) {
          callback({
            success: false,
            errorCode: 'ERR_EXCEED_FREE_TRIAL_QUOTA'
          });
        }
        // Test case 3: Service unavailable error
        else if (message.prompt.includes('test-service-error')) {
          callback({
            success: false,
            errorCode: 'ERR_SERVICE_UNAVAILABLE'
          });
        }
        // Test case 4: Invalid JSON error
        else if (message.prompt.includes('test-invalid-json')) {
          callback({
            success: true,
            optimizedPrompt: 'This is not valid JSON'
          });
        }
        // Success case
        else {
          callback({
            success: true,
            optimizedPrompt: JSON.stringify({
              generated: {
                prompt: "This is an optimized prompt",
                requirements: "These are the requirements"
              }
            })
          });
        }
      }
    },
    getURL: (path) => {
      return `chrome-extension://fake-extension-id/${path}`;
    }
  },
  i18n: {
    getMessage: (key) => {
      // Mock localized strings
      const messages = {
        'errorTitle': 'Error',
        'closeButtonText': 'Close',
        'errorLoginRequired': 'Login is required to use this feature.',
        'errorExceedFreeTrialQuota': 'You have exceeded your free trial quota.',
        'errorExceedDailyQuota': 'You have exceeded your daily quota.',
        'errorExceedMsgLimit': 'You have exceeded the message limit.',
        'errorServiceUnavailable': 'Service is currently unavailable. Please try again later.',
        'errorAIGenerationFailed': 'AI generation failed. Please try again.',
        'pricingButton': 'View Plans',
        'loginFunBlocksButton': 'Login FunBlocks AI Account'
      };
      return messages[key] || key;
    }
  }
};

// Mock document functions
const document = {
  createElement: (tag) => {
    console.log(`Creating element: ${tag}`);
    return {
      id: '',
      className: '',
      style: {},
      appendChild: (child) => {
        console.log('Appending child:', child);
      },
      addEventListener: (event, handler) => {
        console.log(`Adding event listener for ${event}`);
      },
      setAttribute: (attr, value) => {
        console.log(`Setting attribute ${attr}=${value}`);
      },
      textContent: ''
    };
  },
  getElementById: (id) => {
    console.log(`Getting element by ID: ${id}`);
    return {
      classList: {
        add: (className) => {
          console.log(`Adding class ${className} to element with ID ${id}`);
        },
        remove: (className) => {
          console.log(`Removing class ${className} from element with ID ${id}`);
        }
      },
      disabled: false
    };
  },
  body: {
    appendChild: (child) => {
      console.log('Appending child to body:', child);
    }
  },
  querySelectorAll: (selector) => {
    console.log(`Querying selector: ${selector}`);
    return [];
  }
};

// Mock window functions
const window = {
  open: (url) => {
    console.log(`Opening URL: ${url}`);
  }
};

// Error code mapping
const ERROR_CODES = {
  ERR_LOGIN_REQUIRED: 'errorLoginRequired',
  ERR_SERVICE_UNAVAILABLE: 'errorServiceUnavailable',
  ERR_GENERATION_FAILED: 'errorAIGenerationFailed',
  ERR_EXCEED_FREE_TRIAL_QUOTA: 'errorExceedFreeTrialQuota',
  ERR_EXCEED_DAILY_QUOTA: 'errorExceedDailyQuota',
  ERR_EXCEED_MSG_LIMIT: 'errorExceedMsgLimit',
  ERR_INVALID_JSON: 'errorAIGenerationFailed'
};

// Helper function to get error message from error code
function getErrorMessageFromCode(errorCode) {
  // 如果是错误代码，使用本地化字符串
  if (errorCode && ERROR_CODES[errorCode]) {
    return chrome.i18n.getMessage(ERROR_CODES[errorCode]) || errorCode;
  }
  // 否则直接返回错误消息
  return errorCode;
}

// Helper function to show error popup
function showErrorPopup(errorCodeOrMessage) {
  const errorMessage = getErrorMessageFromCode(errorCodeOrMessage);
  console.log('Showing error popup with message:', errorMessage);
  const errorPopup = createErrorPopup(errorMessage);
  document.body.appendChild(errorPopup);
  console.log('Positioning popup');
  console.log('Showing popup with overlay');
  return errorPopup;
}

// Mock extractJSONFromString function
function extractJSONFromString(str) {
  console.log('Extracting JSON from string:', str);
  
  try {
    // Simple JSON parsing for testing
    if (str.includes('{') && str.includes('}')) {
      return JSON.parse(str);
    }
    return null;
  } catch (error) {
    console.error('Error parsing JSON:', error);
    return null;
  }
}

// Mock createErrorPopup function
function createErrorPopup(errorMessage) {
  console.log('Creating error popup with message:', errorMessage);
  
  // Format error message
  let formattedErrorMessage = errorMessage;
  let buttons = [];

  // Handle specific error codes
  if (errorMessage === 'ERR_EXCEED_FREE_TRIAL_QUOTA' || errorMessage === 'ERR_EXCEED_DAILY_QUOTA' || errorMessage === 'ERR_EXCEED_MSG_LIMIT') {
    console.log('Quota error detected, adding pricing button');
    buttons.push({ text: 'View Plans' });
  } else if (errorMessage === 'ERR_LOGIN_REQUIRED') {
    console.log('Login error detected, adding login button');
    buttons.push({ text: 'Login FunBlocks AI Account' });
  }
  
  buttons.push({ text: 'Close' });
  
  console.log('Buttons:', buttons.map(b => b.text).join(', '));
  
  // Return a mock popup object
  return {
    id: 'ai-error-popup',
    className: 'ai-prompt-optimizer-popup ai-error-popup',
    style: {}
  };
}

// Test the error code handling
console.log('=== Testing Login Error ===');
handleOptimizeClick('test-login-error');

console.log('\n=== Testing Quota Error ===');
handleOptimizeClick('test-quota-error');

console.log('\n=== Testing Service Error ===');
handleOptimizeClick('test-service-error');

console.log('\n=== Testing Invalid JSON Error ===');
handleOptimizeClick('test-invalid-json');

// Mock the handleOptimizeClick function
function handleOptimizeClick(testInput) {
  const platform = 'chatgpt.com';
  const inputText = testInput || 'test input';
  
  console.log('Input text:', inputText);
  
  // Update button state
  const button = document.getElementById('ai-prompt-optimizer-button');
  button.classList.add('loading');
  button.disabled = true;
  
  // Send message to background script
  chrome.runtime.sendMessage(
    { action: 'optimizePrompt', prompt: inputText },
    (response) => {
      // Reset button state
      button.classList.remove('loading');
      button.disabled = false;
      
      if (response && response.success && response.optimizedPrompt) {
        console.log('Optimized prompt received:', response.optimizedPrompt);
        
        const jsonResult = extractJSONFromString(response.optimizedPrompt);
        
        // Check if the extracted JSON is valid and has the expected structure
        if (!jsonResult || !jsonResult.generated || (!jsonResult.generated.prompt && !jsonResult.generated.requirements)) {
          console.error('Failed to extract valid JSON from response:', response.optimizedPrompt);
          
          // Show error message for invalid JSON
          showErrorPopup('ERR_INVALID_JSON');
          return;
        }
        
        // Create and show popup with optimized prompt data
        console.log('Valid JSON extracted, creating optimized prompt popup');
      } else {
        console.error('Failed to optimize prompt:', response?.errorCode || 'Unknown error');
        
        // Show error message for API error
        showErrorPopup(response?.errorCode || 'ERR_GENERATION_FAILED');
      }
    }
  );
}
