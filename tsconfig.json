{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "jsx": "react", "strict": true, "sourceMap": true, "outDir": "./dist", "baseUrl": ".", "paths": {"*": ["node_modules/*"]}, "allowJs": true, "checkJs": false}, "include": ["src/**/*", "src/types/*.d.ts"], "exclude": ["node_modules", "dist"], "typeRoots": ["./node_modules/@types", "./src/types"]}