// Popup script for AI Prompt Optimizer

// Update UI with localized strings
function updateUIWithLocalizedStrings() {
  document.getElementById('settingsTitle').textContent = chrome.i18n.getMessage('settingsTitle');
  document.getElementById('languageLabel').textContent = chrome.i18n.getMessage('languageLabel');
  document.getElementById('apiUrlLabel').textContent = chrome.i18n.getMessage('apiUrlLabel');
  document.getElementById('saveButton').textContent = chrome.i18n.getMessage('saveButton');
}

// Load settings from storage
function loadSettings() {
  chrome.runtime.sendMessage({ action: 'getSettings' }, (settings) => {
    document.getElementById('apiUrl').value = settings.apiUrl;
    document.getElementById('language').value = settings.language;
    
    // Update UI language based on current setting
    document.documentElement.lang = settings.language;
  });
}

// Save settings to storage
function saveSettings() {
  const apiUrl = document.getElementById('apiUrl').value.trim();
  const language = document.getElementById('language').value;
  
  if (!apiUrl) {
    showStatus('error', 'API URL cannot be empty');
    return;
  }
  
  chrome.runtime.sendMessage(
    { 
      action: 'saveSettings', 
      settings: { apiUrl, language } 
    },
    (response) => {
      if (response.success) {
        showStatus('success', chrome.i18n.getMessage('successMessage'));
        
        // Update UI language if it was changed
        document.documentElement.lang = language;
        updateUIWithLocalizedStrings();
      } else {
        showStatus('error', chrome.i18n.getMessage('errorMessage'));
      }
    }
  );
}

// Show status message
function showStatus(type, message) {
  const statusElement = document.getElementById('status');
  statusElement.textContent = message;
  statusElement.className = `status ${type}`;
  
  // Hide status after 3 seconds
  setTimeout(() => {
    statusElement.className = 'status';
  }, 3000);
}

// Initialize popup
document.addEventListener('DOMContentLoaded', () => {
  // Update UI with localized strings
  updateUIWithLocalizedStrings();
  
  // Load settings
  loadSettings();
  
  // Add event listener for save button
  document.getElementById('saveButton').addEventListener('click', saveSettings);
  
  // Add event listener for language change
  document.getElementById('language').addEventListener('change', () => {
    // Preview language change immediately in the UI
    const language = document.getElementById('language').value;
    document.documentElement.lang = language;
  });
});
