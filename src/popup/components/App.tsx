import React, { useEffect, useState } from 'react';
import useI18n from '../../common/hooks/useI18n';
import useSettings from '../../common/hooks/useSettings';
import { APP_URL } from '../../utils/Constants';
import { AI_OUTPUT_LANGUAGE_OPTIONS } from '../../utils/LanguageConfig';

const App: React.FC = () => {
  const { getMessage } = useI18n();
  const { settings, loading, saveSettings, updateSetting } = useSettings();
  const [isLoggedIn, setIsLoggedIn] = useState<boolean | null>(null);
  const [username, setUsername] = useState<string>('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    saveSettings().then((success) => {
      if (success) {
        console.log('Settings saved successfully');
      } else {
        console.error('Failed to save settings');
      }
    });
  };

  // Set document language based on current UI language setting or browser language
  useEffect(() => {
    const uiLang = settings.uiLanguage || settings.language; // Fallback to legacy language setting
    if (uiLang) {
      document.documentElement.lang = uiLang;
    } else {
      // Default to browser language
      const browserLang = navigator.language.split('-')[0];
      if (browserLang === 'zh') {
        updateSetting('uiLanguage', 'zh');
        // Also set AI output language if not set
        if (!settings.aiOutputLanguage) {
          updateSetting('aiOutputLanguage', 'zh');
        }
      } else {
        updateSetting('uiLanguage', 'en');
        // Also set AI output language if not set
        if (!settings.aiOutputLanguage) {
          updateSetting('aiOutputLanguage', 'en');
        }
      }
    }
  }, [settings.uiLanguage, settings.language]);


  useEffect(() => {
    saveSettings();
  }, [settings])

  // Check login status
  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        // Send message to background script to check login status
        chrome.runtime.sendMessage({ action: 'checkLoginStatus' }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('Error checking login status:', chrome.runtime.lastError);
            setIsLoggedIn(false);
          } else if (response && response.isLoggedIn) {
            setIsLoggedIn(true);
            setUsername(response.username || '');
          } else {
            setIsLoggedIn(false);
          }
        });
      } catch (error) {
        console.error('Error checking login status:', error);
        setIsLoggedIn(false);
      }
    };

    checkLoginStatus();
  }, []);

  const handleLogin = () => {
    // Open login page in a new tab
    window.open(`${APP_URL}/#/login?source=extension`);
  };

  const openPricingPage = () => {
    window.open(`${APP_URL}/#/aiplans`, '_blank');
  };

  const openOptionsPage = () => {
    chrome.runtime.openOptionsPage();
  };

  const openPromptOptimizer = () => {
    window.open('https://www.funblocks.net/prompt-optimizer', '_blank');
  };

  if (loading) {
    return <div className="container">{getMessage('loadingText')}</div>;
  }

  return (
    <div className="container">
      {/* Login Status Section */}
      <div className="login-status">
        {
          isLoggedIn ? (
            <div className="logged-in">
              <span className="username">{username}</span>
              <span className="status-indicator logged-in-indicator"></span>
            </div>
          ) : (
            <div className="logged-out">
              <span>{getMessage('notLoggedIn')}</span>
              <button className="login-button" onClick={handleLogin}>
                {getMessage('loginFunBlocksButton')}
              </button>
            </div>
          )
        }
      </div>

      {/* Menu Items */}
      <div className="menu-items">
        {/* AI Output Language Selector */}
        <div className="menu-item language-selector">
          <div className="menu-icon">🌐</div>
          <div className="language-selector-content">
            <label htmlFor="aiOutputLanguage">{getMessage('aiOutputLanguageLabel')}</label>
            <select
              id="aiOutputLanguage"
              value={settings.aiOutputLanguage || settings.language || 'en'}
              onChange={(e) => {
                updateSetting('aiOutputLanguage', e.target.value);
              }}
            >
              {AI_OUTPUT_LANGUAGE_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="menu-item" onClick={openPromptOptimizer}>
          <img src="../images/icon.svg" alt="" className="menu-icon" />
          <span>FunBlocks AI Prompt Optimizer</span>
        </div>

        <div className="menu-item" onClick={openPricingPage}>
          <div className="menu-icon">🎁</div> <span>Upgrade AI Plan</span>
        </div>

        <div className="menu-item" onClick={openOptionsPage}>
          <div className="menu-icon">⚙️</div> <span>Settings</span>
        </div>
      </div>
    </div>
  );
};

export default App;
