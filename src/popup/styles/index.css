body {
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  background-color: transparent;
  min-height: 100vh;
  line-height: 1.6;
  border-radius: 16px;
}

.container {
  width: 320px;
  padding: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  /* border-radius: 16px; */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  animation: slideIn 0.4s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

h1 {
  font-size: 20px;
  margin: 0;
  padding: 20px;
  color: #2d3748;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.form-group {
  margin-bottom: 20px;
  padding: 0 20px;
}

label {
  display: block;
  margin-bottom: 12px;
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

input[type="text"],
select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  box-sizing: border-box;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  color: #2d3748;
}

input[type="text"]:focus,
select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

button:hover::before {
  left: 100%;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.status {
  margin-top: 16px;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
  display: none;
}

.status.success {
  background-color: #d4edda;
  color: #155724;
  display: block;
}

.status.error {
  background-color: #f8d7da;
  color: #721c24;
  display: block;
}

/* Login Status Styles */
.login-status {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  height: 30px;
  position: relative;
  overflow: hidden;
  /* display: flex;
  flex-direction: row; */
  /* align-items: center;
  justify-content: space-between; */
}

.login-status::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
  pointer-events: none;
}

.logged-in {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  width: '-webkit-fill-available';
  height: 100%;
  align-self: center;
  z-index: 1;
}

.username {
  font-weight: 600;
  color: #ffffff;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
}

.logged-in-indicator {
  background-color: #4ade80;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.logged-out {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: '-webkit-fill-available';
  position: relative;
  z-index: 1;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.login-button {
  background: linear-gradient(135deg, #10a37f 0%, #0d8c6d 100%);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(16, 163, 127, 0.3);
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(16, 163, 127, 0.4);
}

.loading-indicator {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
}

/* Legacy styles - cleaned up and modernized above */

.menu-items {
  padding: 20px 20px 0 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.menu-items .menu-item:last-child {
  margin-bottom: 20px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s;
}

.menu-item:hover::before {
  left: 100%;
}

.menu-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
}

.menu-item:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.menu-icon {
  width: 24px;
  height: 24px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.menu-item:hover .menu-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.menu-icon img {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}

.menu-item span {
  font-size: 15px;
  color: #2d3748;
  font-weight: 600;
  letter-spacing: -0.2px;
  transition: color 0.3s ease;
}

.menu-item:hover span {
  color: #1a202c;
}

/* Language Selector Styles */
.language-selector {
  cursor: default !important;
  padding: 12px 16px !important;
}

.language-selector:hover {
  transform: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%) !important;
}

.language-selector::before {
  display: none !important;
}

.language-selector-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.language-selector-content label {
  font-size: 13px;
  font-weight: 600;
  color: #4a5568;
  margin: 0;
  line-height: 1.2;
}

.language-selector-content select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 13px;
  background: rgba(255, 255, 255, 0.9);
  color: #2d3748;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.language-selector-content select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.language-selector-content select:hover {
  border-color: #cbd5e0;
}

/* Remove duplicate styles - these are now handled above */

/* Additional enhancements */
.status {
  margin: 20px;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  display: none;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status.success {
  background: linear-gradient(135deg, rgba(74, 222, 128, 0.9) 0%, rgba(34, 197, 94, 0.9) 100%);
  color: #ffffff;
  display: block;
  box-shadow: 0 4px 15px rgba(74, 222, 128, 0.3);
}

.status.error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
  color: #ffffff;
  display: block;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    color: #f7fafc;
  }

  .container {
    background: rgba(45, 55, 72, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  h1 {
    color: #f7fafc;
    background: linear-gradient(135deg, rgba(45, 55, 72, 0.9) 0%, rgba(26, 32, 44, 0.9) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  label {
    color: #e2e8f0;
  }

  input[type="text"],
  select {
    background: rgba(26, 32, 44, 0.9);
    color: #f7fafc;
    border-color: #4a5568;
  }

  input[type="text"]:focus,
  select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
  }

  .login-status {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  }

  .menu-item {
    background: linear-gradient(135deg, rgba(45, 55, 72, 0.8) 0%, rgba(26, 32, 44, 0.8) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .menu-item:hover {
    background: linear-gradient(135deg, rgba(45, 55, 72, 0.95) 0%, rgba(26, 32, 44, 0.95) 100%);
  }

  .menu-item span {
    color: #e2e8f0;
  }

  .menu-item:hover span {
    color: #f7fafc;
  }

  /* Dark mode language selector styles */
  .language-selector-content label {
    color: #a0aec0;
  }

  .language-selector-content select {
    background: rgba(26, 32, 44, 0.9);
    color: #f7fafc;
    border-color: #4a5568;
  }

  .language-selector-content select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  }

  .language-selector-content select:hover {
    border-color: #718096;
  }
}

/* Responsive design */
@media (max-width: 360px) {
  .container {
    width: 280px;
  }

  .menu-item {
    padding: 14px;
  }

  .menu-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
  }

  .menu-item span {
    font-size: 14px;
  }
}
