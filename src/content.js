// Content script for AI Prompt Optimizer

import { APP_URL } from "./utils/Constants";
import { ERROR_CODES } from "./utils/ErrorCode";
import { extractJSONFromString } from "./utils/jsonStringUtil";
import { markdownToHtml } from "./utils/markdownUtil";
import { initLocalization, getLocalizedString } from "./utils/contentLocalization";

// 通用的创建弹窗函数
function createPopup(options) {
  // 移除所有可能存在的弹窗和遮罩层
  const existingPopups = document.querySelectorAll('.ai-prompt-optimizer-popup');
  const existingOverlay = document.getElementById('ai-prompt-optimizer-overlay');

  existingPopups.forEach(popup => popup.remove());
  if (existingOverlay) existingOverlay.remove();

  // 弹窗类型和ID
  const popupType = options.type || 'default';
  const popupId = options.id || `ai-${popupType}-popup`;

  // 创建弹窗容器
  const popup = document.createElement('div');
  popup.id = popupId;
  popup.className = `ai-prompt-optimizer-popup ${popupType === 'error' ? 'ai-error-popup' : ''}`;

  // 设置初始样式以确保动画效果
  popup.style.opacity = '0';
  popup.style.transform = 'translateY(10px)';

  // 创建弹窗内容容器
  const popupContent = document.createElement('div');
  popupContent.className = 'ai-prompt-optimizer-popup-content';

  // 创建弹窗头部
  const popupHeader = document.createElement('div');
  popupHeader.className = `ai-prompt-optimizer-popup-header ${popupType === 'error' ? 'error-header' : ''}`;
  popupHeader.textContent = options.title || '';

  // 创建弹窗主体
  const popupBody = document.createElement('div');
  popupBody.className = 'ai-prompt-optimizer-popup-body';

  // 使用内容生成函数填充主体内容
  if (typeof options.contentGenerator === 'function') {
    options.contentGenerator(popupBody, popup);
  }

  // 创建弹窗底部
  const popupFooter = document.createElement('div');
  popupFooter.className = 'ai-prompt-optimizer-popup-footer';

  // 添加按钮
  if (options.buttons && Array.isArray(options.buttons)) {
    options.buttons.forEach(button => {
      const buttonElement = document.createElement('button');
      buttonElement.className = `ai-prompt-optimizer-popup-button ${button.type || 'cancel'}`;
      buttonElement.textContent = button.text || '';

      if (typeof button.onClick === 'function') {
        buttonElement.addEventListener('click', (e) => {
          button.onClick(e, popup);
        });
      }

      popupFooter.appendChild(buttonElement);
    });
  }

  // 组装弹窗
  popupContent.appendChild(popupHeader);
  popupContent.appendChild(popupBody);
  popupContent.appendChild(popupFooter);
  popup.appendChild(popupContent);

  return popup;
}

// Platform-specific selectors for input fields and message containers
const PLATFORM_SELECTORS = {
  'chatgpt.com': {
    inputSelector: '#prompt-textarea',
    containerSelector: '[data-testid="composer-footer-actions"]',
    insertPosition: 'beforeend',

    messageSelector: '[data-message-id]',
    messageActionsSelector: '[data-testid="copy-turn-action-button"]',
    messageActionsInsertPosition: 'beforeend'
  },
  'gemini.google.com': {
    inputSelector: 'div[contenteditable="true"]',
    containerSelector: 'toolbox-drawer',
    insertPosition: 'afterend',

    querySelector: '.query-text',
    queryActionsSelector: '.edit-button',
    queryActionsInsertPosition: 'afterend',

    messageSelector: 'message-content',
    messageActionsSelector: 'message-actions',
    messageActionsInsertPosition: 'afterend'
  },
  'claude.ai': {
    inputSelector: 'div[contenteditable="true"]',
    containerSelector: 'div[contenteditable="true"]',
    insertPosition: 'afterend',

    querySelector: '.font-user-message',
    queryActionsSelector: '.flex.items-stretch',
    queryActionsInsertPosition: 'afterbegin',

    messageSelector: '.font-claude-message',
    messageActionsSelector: '.flex.items-stretch',
    messageActionsInsertPosition: 'afterbegin'
  },
  'perplexity.ai': {
    inputSelector: 'textarea#ask-input',
    containerSelector: 'textarea#ask-input',
    insertPosition: 'afterend',


    querySelector: '.group\\/title',
    queryActionsSelector: '.group-hover\\:opacity-100.opacity-0',
    queryActionsInsertPosition: 'afterbegin',

    messageSelector: '.prose',
    messageActionsSelector: '.gap-x-xs',
    messageActionsInsertPosition: 'afterbegin'
  },
  'chat.deepseek.com': {
    inputSelector: 'textarea#chat-input',
    containerSelector: 'textarea#chat-input',
    insertPosition: 'afterend',

    querySelector: '.user-query',
    queryActionsSelector: '.ds-icon-button',
    queryActionsInsertPosition: 'beforebegin',

    messageSelector: '.ds-markdown',
    messageActionsSelector: '.ds-icon-button',
    messageActionsInsertPosition: 'beforebegin'
  }
};

// Get current platform based on URL
function getCurrentPlatform() {
  const hostname = window.location.hostname;
  for (const platform in PLATFORM_SELECTORS) {
    if (hostname.includes(platform)) {
      return platform;
    }
  }
  return null;
}

// Note: getLocalizedString is now imported from contentLocalization.js

// Create optimize button
function createOptimizeButton() {
  const button = document.createElement('button');
  button.id = 'ai-prompt-optimizer-button';

  // Set button type to 'button' to prevent form submission
  button.setAttribute('type', 'button');

  // Default styling for all platforms - icon only button
  button.className = 'ai-prompt-optimizer-button';

  // Set a custom attribute to help identify our button
  button.setAttribute('data-ai-prompt-optimizer', 'true');

  // Create img element for the icon
  const img = document.createElement('img');
  img.src = chrome.runtime.getURL('images/button-icon.svg');
  img.className = 'optimizer-icon';
  img.alt = getLocalizedString('optimizeButtonText');

  button.appendChild(img);
  button.setAttribute('aria-label', getLocalizedString('optimizeButtonText'));

  // Create tooltip elements
  const tooltipText = document.createElement('span');
  tooltipText.className = 'tooltip-text';
  tooltipText.textContent = getLocalizedString('optimizeButtonText');

  const tooltipArrow = document.createElement('span');
  tooltipArrow.className = 'tooltip-arrow';

  button.appendChild(tooltipText);
  button.appendChild(tooltipArrow);
  // }

  return button;
}

// Create critical thinking assistant button
function createCriticalThinkingAssistantButton() {
  const button = document.createElement('button');
  button.id = 'ai-critical-thinking-assistant-button';
  button.className = 'ai-prompt-optimizer-button critical-thinking-assistant-button';

  // Create img element for the icon (using the same icon as optimize button)
  const img = document.createElement('img');
  img.src = chrome.runtime.getURL('images/button-icon.svg');
  img.className = 'optimizer-icon';
  img.alt = getLocalizedString('criticalThinkingAssistantTriggerTooltipsText');

  button.appendChild(img);
  button.setAttribute('aria-label', getLocalizedString('criticalThinkingAssistantTriggerTooltipsText'));

  // Create tooltip element (without arrow)
  const tooltipText = document.createElement('span');
  tooltipText.className = 'tooltip-text';
  tooltipText.textContent = getLocalizedString('criticalThinkingAssistantTriggerTooltipsText');

  button.appendChild(tooltipText);

  return button;
}

// Get input text from the platform's input field
function getInputText(platform) {
  const selector = PLATFORM_SELECTORS[platform].inputSelector;
  const inputElement = document.querySelector(selector);

  if (!inputElement) return '';

  // Handle different input types
  if (inputElement.tagName.toLowerCase() === 'textarea' || inputElement.tagName.toLowerCase() === 'input') {
    return inputElement.value;
  } else if (inputElement.getAttribute('contenteditable') === 'true') {
    return inputElement.textContent;
  }

  return '';
}

// Set input text to the platform's input field
function setInputText(platform, text) {
  const selector = PLATFORM_SELECTORS[platform].inputSelector;
  const inputElement = document.querySelector(selector);

  if (!inputElement) return;

  // Ensure Markdown line breaks are preserved
  // In Markdown, a single newline is typically ignored unless there are two consecutive newlines
  // or a line ends with two spaces followed by a newline

  // Handle different input types
  if (inputElement.tagName.toLowerCase() === 'textarea' || inputElement.tagName.toLowerCase() === 'input') {
    // For textarea/input elements, we can directly set the value with preserved line breaks
    inputElement.value = text;
    // Trigger input event to activate send button
    inputElement.dispatchEvent(new Event('input', { bubbles: true }));
  } else if (inputElement.getAttribute('contenteditable') === 'true') {
    // For contenteditable elements, we need to handle HTML formatting
    // Convert Markdown line breaks to HTML breaks
    // Replace single newlines that follow two spaces with <br>
    const htmlText = text
      .replace(/ {2,}\n/g, '</p><p>')
      // Replace double newlines with paragraph breaks
      .replace(/\n\n/g, '</p><br><p>')
      // Replace remaining single newlines with <br>
      .replace(/\n/g, '</p><p>');

    // Wrap in paragraph tags if not already wrapped
    const wrappedHtml = htmlText.startsWith('<p>') ? htmlText : `<p>${htmlText}</p>`;

    // Set the HTML content
    inputElement.innerHTML = wrappedHtml;

    // Trigger input event to activate send button
    inputElement.dispatchEvent(new Event('input', { bubbles: true }));
  }
}

// Insert button into the chat interface
function insertButton(platform) {
  const config = PLATFORM_SELECTORS[platform];
  const container = document.querySelector(config.containerSelector);

  if (!container) return;

  // Check if button already exists
  if (document.getElementById('ai-prompt-optimizer-button')) return;

  const button = createOptimizeButton();

  console.log('platform..........', platform, button)

  if (platform === 'chat.deepseek.com') {
    document.querySelectorAll('.ds-button')[1]?.insertAdjacentElement('afterend', button);
  } else if (platform === 'claude.ai') {
    let ele = document.querySelector('[data-testid="input-menu-tools"]');
    while (ele && ele.parentNode && (ele.parentNode.childNodes.length <= 1 || ele.parentNode.childNodes[1].childNodes?.length == 0)) {
      ele = ele.parentNode;
    }
    if (ele && ele.parentNode) {
      ele.parentNode.insertBefore(button, ele.nextSibling);
    }
  } else if (platform === 'perplexity.ai') {
    let textarea = document.querySelector(config.inputSelector);
    let parent = textarea?.parentNode;

    while (parent && (parent.childNodes.length <= 1 || Array.from(parent.childNodes).includes(textarea))) {
      parent = parent.parentNode;
    }

    parent.childNodes[parent.childNodes.length - 1].insertAdjacentElement('afterbegin', button);
  } else {
    // Insert button based on platform configuration
    if (config.insertPosition === 'afterend') {
      container.insertAdjacentElement('afterend', button);
    } else if (config.insertPosition === 'beforebegin') {
      container.insertAdjacentElement('beforebegin', button);
    } else if (config.insertPosition === 'beforeend') {
      // Insert as the last child of the container
      container.appendChild(button);
    }
  }

  // For ChatGPT, ensure the button has the correct type attribute
  if (platform === 'chatgpt.com') {
    button.setAttribute('type', 'button');
  }

  // Add click event listener with explicit capture to prevent event bubbling
  button.addEventListener('click', handleOptimizeClick, true);

  // Check initial input field content and set button state
  updateOptimizeButtonState(platform);

  // Set up input field monitoring
  setupInputFieldMonitoring(platform);
}

// Create popup for displaying optimized prompt
function createOptimizedPromptPopup(optimizedData, platform) {
  // Extract prompt and requirements from the data
  const optimizedPrompt = typeof optimizedData === 'object' && optimizedData.generated && optimizedData.generated.prompt
    ? optimizedData.generated.prompt
    : (typeof optimizedData === 'string' ? optimizedData : '');

  const requirements = typeof optimizedData === 'object' && optimizedData.generated && optimizedData.generated.requirements
    ? optimizedData.generated.requirements
    : '';

  return createPopup({
    type: 'optimized-prompt',
    id: 'ai-prompt-optimizer-popup',
    title: getLocalizedString('optimizedPromptTitle') || 'Optimized Prompt',
    contentGenerator: (popupBody) => {
      // 添加需求分析部分（如果有）
      if (requirements) {
        const requirementsLabel = document.createElement('div');
        requirementsLabel.className = 'ai-prompt-optimizer-popup-label';
        requirementsLabel.textContent = getLocalizedString('requirementsLabel') || 'Analysis:';
        popupBody.appendChild(requirementsLabel);

        const requirementsText = document.createElement('div');
        requirementsText.className = 'ai-prompt-optimizer-popup-requirements';
        requirementsText.textContent = requirements;
        popupBody.appendChild(requirementsText);
      }

      // 添加优化后的提示部分
      const promptLabel = document.createElement('div');
      promptLabel.className = 'ai-prompt-optimizer-popup-label prompt-label';
      promptLabel.textContent = getLocalizedString('promptLabel') || 'Optimized Prompt:';
      popupBody.appendChild(promptLabel);

      const promptContainer = document.createElement('div');
      promptContainer.className = 'ai-prompt-optimizer-popup-prompt-container';

      const promptText = document.createElement('div');
      promptText.className = 'ai-prompt-optimizer-popup-text';

      // 如果提示包含Markdown格式，则渲染为HTML
      if (optimizedPrompt.includes('\n') ||
        optimizedPrompt.includes('*') ||
        optimizedPrompt.includes('#') ||
        optimizedPrompt.includes('`')) {
        promptText.innerHTML = markdownToHtml(optimizedPrompt);
      } else {
        promptText.textContent = optimizedPrompt;
      }

      promptContainer.appendChild(promptText);
      popupBody.appendChild(promptContainer);
    },
    buttons: [
      {
        type: 'confirm',
        text: getLocalizedString('confirmButtonText') || 'Confirm',
        onClick: (_, popup) => {
          setInputText(platform, optimizedPrompt);
          const overlay = document.getElementById('ai-prompt-optimizer-overlay');
          closePopup(popup, overlay);
        }
      },
      {
        type: 'cancel',
        text: getLocalizedString('cancelButtonText') || 'Cancel',
        onClick: (_, popup) => {
          const overlay = document.getElementById('ai-prompt-optimizer-overlay');
          closePopup(popup, overlay);
        }
      }
    ]
  });
}

// Handle question_optimize response
function handleQuestionOptimizeResponse(responseData, platform) {
  // Extract optimized questions from the response
  const optimizedQuestions = responseData.generated && responseData.generated.optimized
    ? responseData.generated.optimized
    : [];

  if (!optimizedQuestions || optimizedQuestions.length === 0) {
    showErrorPopup(ERROR_CODES.GENERATION_FAILED);
    return;
  }

  // Create popup with optimized questions
  const popup = createPopup({
    type: 'question-optimize',
    id: 'ai-question-optimize-popup',
    title: getLocalizedString('optimizedQuestionsTitle') || '更好的提问',
    contentGenerator: (popupBody) => {
      // Create list of optimized questions
      const questionsList = document.createElement('ul');
      questionsList.className = 'ai-prompt-optimizer-popup-list questions-list';

      optimizedQuestions.forEach(question => {
        const questionItem = document.createElement('li');
        questionItem.className = 'ai-prompt-optimizer-popup-list-item question-item';
        questionItem.textContent = question;

        // Add click event to insert the question into the input field
        questionItem.addEventListener('click', () => {
          setInputText(platform, question);
          const overlay = document.getElementById('ai-prompt-optimizer-overlay');
          closePopup(popup, overlay);
        });

        questionsList.appendChild(questionItem);
      });

      popupBody.appendChild(questionsList);
    },
    buttons: [
      {
        type: 'cancel',
        text: getLocalizedString('cancelButtonText') || 'Cancel',
        onClick: (_, popup) => {
          const overlay = document.getElementById('ai-prompt-optimizer-overlay');
          closePopup(popup, overlay);
        }
      }
    ]
  });

  // Show the popup
  document.body.appendChild(popup);
  positionPopup(popup);
  showPopupWithOverlay(popup);
}

// Create form fields based on API response
function createFormFields(formArgs) {
  const formContainer = document.createElement('div');
  formContainer.className = 'ai-prompt-optimizer-form-container';

  formArgs.forEach(field => {
    const fieldContainer = document.createElement('div');
    fieldContainer.className = 'ai-prompt-optimizer-form-field';

    // Create label
    const label = document.createElement('label');
    label.className = 'ai-prompt-optimizer-form-label';
    label.textContent = field.label;
    label.setAttribute('for', `field-${field.name}`);
    fieldContainer.appendChild(label);

    // Create description if provided
    if (field.description) {
      const description = document.createElement('div');
      description.className = 'ai-prompt-optimizer-form-description';
      description.textContent = field.description;
      fieldContainer.appendChild(description);
    }

    // Create input field based on type
    let inputElement;
    let otherInputContainer = null;
    let otherInputElement = null;
    let hasOtherOption = false;

    if (field.type === 'textarea') {
      inputElement = document.createElement('textarea');
      inputElement.rows = 3;
    } else if (field.type === 'select') {
      // Create a wrapper for select and potential "Other" input
      const selectWrapper = document.createElement('div');
      selectWrapper.className = 'ai-prompt-optimizer-select-wrapper';

      // Create the select element
      inputElement = document.createElement('select');

      // Add options
      if (field.options && Array.isArray(field.options)) {
        // Check if there's an "Other" option
        hasOtherOption = field.options.some(option =>
          option.value === null
        );

        field.options.forEach(option => {
          const optionElement = document.createElement('option');
          optionElement.value = option.value !== null ? option.value : 'other';
          optionElement.textContent = option.label;

          // Add title attribute for tooltip on hover
          optionElement.title = option.label;

          // Mark "Other" option for special handling
          if (option.value === null) {
            optionElement.dataset.isOther = 'true';
          }

          inputElement.appendChild(optionElement);
        });
      }

      // Add the select to the wrapper
      selectWrapper.appendChild(inputElement);

      // Create "Other" input field if needed
      if (hasOtherOption) {
        otherInputContainer = document.createElement('div');
        otherInputContainer.className = 'ai-prompt-optimizer-other-input-container';
        otherInputContainer.style.display = 'none'; // Initially hidden

        otherInputElement = document.createElement('input');
        otherInputElement.type = 'text';
        otherInputElement.id = `field-${field.name}-other`;
        otherInputElement.className = 'ai-prompt-optimizer-form-input other-input';
        otherInputElement.placeholder = getLocalizedString('otherInputPlaceholder') || 'Please specify...';

        otherInputContainer.appendChild(otherInputElement);
        selectWrapper.appendChild(otherInputContainer);

        // Add change event listener to show/hide the "Other" input
        inputElement.addEventListener('change', function () {
          const selectedOption = this.options[this.selectedIndex];
          if (selectedOption.dataset.isOther === 'true') {
            otherInputContainer.style.display = 'block';
            otherInputElement.focus();
          } else {
            otherInputContainer.style.display = 'none';
          }
        });
      }

      // Replace inputElement with the wrapper for appending to the field container
      fieldContainer.appendChild(selectWrapper);
    } else {
      // Default to text input
      inputElement = document.createElement('input');
      inputElement.type = 'text';
    }

    // Set common attributes
    inputElement.id = `field-${field.name}`;
    inputElement.name = field.name;
    inputElement.className = `ai-prompt-optimizer-form-input ${field.type}`;

    // Set initial value if provided
    if (field.value) {
      if (field.type === 'select') {
        // For select, find the matching option
        const options = inputElement.querySelectorAll('option');
        for (let i = 0; i < options.length; i++) {
          if (options[i].value === field.value) {
            options[i].selected = true;
            break;
          }
        }
      } else {
        inputElement.value = field.value;
      }
    }

    // Only append the input element directly if it's not a select
    // (selects are already appended within their wrapper)
    if (field.type !== 'select') {
      fieldContainer.appendChild(inputElement);
    }

    formContainer.appendChild(fieldContainer);
  });

  return formContainer;
}

// Handle prompt_detail_form response
function handlePromptDetailFormResponse(responseData, platform, app) {
  const fundamentalRequirements = responseData.generated && responseData.generated.fundamental_requirements
    ? responseData.generated.fundamental_requirements
    : '';

  const isMoreInfoNeeded = responseData.generated && responseData.generated.is_more_info_needed;

  const reasoningForMoreInfo = responseData.generated && responseData.generated.reasoning_for_more_info
    ? responseData.generated.reasoning_for_more_info
    : '';

  const formArgs = responseData.generated && responseData.generated.form && responseData.generated.form.args
    ? responseData.generated.form.args
    : [];

  const optimizedPrompt = responseData.generated && responseData.generated.optimized_prompt
    ? responseData.generated.optimized_prompt
    : '';

  // If form fields are provided and more info is needed, show form
  if (formArgs.length > 0) {
    const popup = createPopup({
      type: 'prompt-detail-form',
      id: 'ai-prompt-detail-form-popup',
      title: getLocalizedString('promptDetailFormTitle') || '优化指令',
      contentGenerator: (popupBody) => {
        // Add fundamental requirements section
        if (fundamentalRequirements) {
          const requirementsLabel = document.createElement('div');
          requirementsLabel.className = 'ai-prompt-optimizer-popup-label';
          requirementsLabel.textContent = getLocalizedString('fundamentalRequirementsLabel') || '基本需求:';
          popupBody.appendChild(requirementsLabel);

          const requirementsText = document.createElement('div');
          requirementsText.className = 'ai-prompt-optimizer-popup-requirements';
          requirementsText.textContent = fundamentalRequirements;
          popupBody.appendChild(requirementsText);
        }

        // Add reasoning for more info section
        // if (reasoningForMoreInfo) {
        //   const reasoningLabel = document.createElement('div');
        //   reasoningLabel.className = 'ai-prompt-optimizer-popup-label';
        //   reasoningLabel.textContent = getLocalizedString('reasoningForMoreInfoLabel') || '需要更多信息:';
        //   popupBody.appendChild(reasoningLabel);

        //   const reasoningText = document.createElement('div');
        //   reasoningText.className = 'ai-prompt-optimizer-popup-reasoning';
        //   reasoningText.textContent = reasoningForMoreInfo;
        //   popupBody.appendChild(reasoningText);
        // }

        // Add form fields
        const formLabel = document.createElement('div');
        formLabel.className = 'ai-prompt-optimizer-popup-label';
        formLabel.textContent = getLocalizedString('additionalInfoLabel') || '请提供更多信息:';
        popupBody.appendChild(formLabel);

        const formContainer = createFormFields(formArgs);
        popupBody.appendChild(formContainer);

        // Add optimized prompt section
        // if (optimizedPrompt) {
        //   const promptLabel = document.createElement('div');
        //   promptLabel.className = 'ai-prompt-optimizer-popup-label prompt-label';
        //   promptLabel.textContent = getLocalizedString('optimizedPromptLabel') || '优化后的提示:';
        //   popupBody.appendChild(promptLabel);

        //   const promptContainer = document.createElement('div');
        //   promptContainer.className = 'ai-prompt-optimizer-popup-prompt-container';

        //   const promptText = document.createElement('div');
        //   promptText.className = 'ai-prompt-optimizer-popup-text';

        //   // Render as HTML if contains Markdown
        //   if (optimizedPrompt.includes('\n') ||
        //     optimizedPrompt.includes('*') ||
        //     optimizedPrompt.includes('#') ||
        //     optimizedPrompt.includes('`')) {
        //     promptText.innerHTML = markdownToHtml(optimizedPrompt);
        //   } else {
        //     promptText.textContent = optimizedPrompt;
        //   }

        //   promptContainer.appendChild(promptText);
        //   popupBody.appendChild(promptContainer);
        // }
      },
      buttons: [
        {
          type: 'confirm',
          text: getLocalizedString('confirmButtonText') || 'Confirm',
          onClick: (_, popup) => {
            // Collect form values
            const formValues = [];
            formArgs.forEach(field => {
              const inputElement = document.getElementById(`field-${field.name}`);
              if (inputElement) {
                let value = '';
                let isEmpty = false;

                // Check if this is a select field
                if (field.type === 'select') {
                  const selectedOption = inputElement.options[inputElement.selectedIndex];

                  if (selectedOption.dataset.isOther === 'true') {
                    // Get value from the "Other" input field
                    const otherInput = document.getElementById(`field-${field.name}-other`);
                    if (otherInput && otherInput.value.trim()) {
                      value = otherInput.value.trim();
                    } else {
                      // Mark as empty if "Other" is selected but no value is provided
                      isEmpty = true;
                    }
                  } else {
                    // Use the selected option's text (label) instead of value
                    value = selectedOption.textContent;
                  }
                } else {
                  // For non-select fields, use the value as before
                  value = inputElement.value.trim();
                  // Check if the value is empty
                  if (!value) {
                    isEmpty = true;
                  }
                }

                // Only add non-empty values to the form values array
                if (!isEmpty) {
                  formValues.push({
                    label: field.label,
                    value: value
                  });
                }
              }
            });

            // Combine optimized prompt with form values
            let finalPrompt = app === 'prompt_image_form' ? '## Generate an image: ' : getInputText(platform);

            // Only add additional info section if there are non-empty form values
            if (formValues.length > 0) {
              finalPrompt += '\n\n';

              if (app !== 'prompt_image_form') {
                finalPrompt += getLocalizedString('additionalInfoPrefix') || '[additional info or requirements]:';
                finalPrompt += '\n';
              }

              formValues.forEach(item => {
                finalPrompt += `- ${item.label}: ${item.value};\n`;
              });
            }

            // Set the final prompt in the input field
            setInputText(platform, finalPrompt);

            // Close the popup
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('cancelButtonText') || 'Cancel',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ]
    });

    // Show the popup
    document.body.appendChild(popup);
    positionPopup(popup);
    showPopupWithOverlay(popup);
  } else {
    // If no form fields or more info not needed, just show the optimized prompt
    const popup = createOptimizedPromptPopup({
      generated: {
        prompt: optimizedPrompt,
        requirements: fundamentalRequirements
      }
    }, platform);

    document.body.appendChild(popup);
    positionPopup(popup);
    showPopupWithOverlay(popup);
  }
}

// Create popup for displaying critical analysis
function createCriticalAnalysisPopup(analysisData, platform) {
  return createPopup({
    type: 'critical-analysis',
    id: 'ai-critical-analysis-popup',
    title: getLocalizedString('criticalAnalysisTitle') || 'Critical Analysis',
    contentGenerator: (popupBody, popup) => {
      // Create a container for the analysis content with proper scrolling
      const analysisContainer = document.createElement('div');
      analysisContainer.className = 'critical-analysis-content';

      // Convert markdown to HTML using the marked library
      const htmlContent = markdownToHtml(analysisData);
      analysisContainer.innerHTML = htmlContent;

      // Ensure the popup body doesn't have conflicting overflow settings
      popupBody.style.overflow = 'visible';
      popupBody.style.maxHeight = 'none';

      popupBody.appendChild(analysisContainer);
    },
    buttons: [
      {
        type: 'confirm',
        text: getLocalizedString('copyButtonText') || 'Copy',
        onClick: (_, popup) => {
          // Copy the analysis content to clipboard
          const analysisContent = popup.querySelector('.critical-analysis-content');
          if (analysisContent) {
            // Get the text content without HTML tags
            const textContent = analysisContent.innerText || analysisContent.textContent;

            // Use the modern clipboard API if available, fallback to older method
            if (navigator.clipboard && navigator.clipboard.writeText) {
              navigator.clipboard.writeText(textContent).then(() => {
                console.log('Critical analysis copied to clipboard');
                // Optionally show a brief success indicator
                const button = popup.querySelector('.ai-prompt-optimizer-popup-button.confirm');
                if (button) {
                  const originalText = button.textContent;
                  button.textContent = '✓ Copied';
                  setTimeout(() => {
                    button.textContent = originalText;
                  }, 2000);
                }
              }).catch(err => {
                console.error('Failed to copy to clipboard:', err);
                // Fallback to manual selection
                fallbackCopyToClipboard(textContent);
              });
            } else {
              // Fallback for older browsers
              fallbackCopyToClipboard(textContent);
            }
          }
        }
      },
      {
        type: 'cancel',
        text: getLocalizedString('cancelButtonText') || 'Close',
        onClick: (_, popup) => {
          const overlay = document.getElementById('ai-prompt-optimizer-overlay');
          closePopup(popup, overlay);
        }
      }
    ]
  });
}

// Fallback function for copying to clipboard in older browsers
function fallbackCopyToClipboard(text) {
  const textArea = document.createElement('textarea');
  textArea.value = text;
  textArea.style.position = 'fixed';
  textArea.style.left = '-999999px';
  textArea.style.top = '-999999px';
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    const successful = document.execCommand('copy');
    if (successful) {
      console.log('Critical analysis copied to clipboard (fallback method)');
    } else {
      console.error('Failed to copy to clipboard using fallback method');
    }
  } catch (err) {
    console.error('Error copying to clipboard:', err);
  }

  document.body.removeChild(textArea);
}

// Create popup for displaying related questions and topics
function createRelatedQuestionsTopicsPopup(relatedData, platform) {
  // Extract related questions and topics from the data
  const relatedQuestions = typeof relatedData === 'object' && relatedData.generated && relatedData.generated.related_questions
    ? relatedData.generated.related_questions
    : [];

  const relatedTopics = typeof relatedData === 'object' && relatedData.generated && relatedData.generated.related_topics
    ? relatedData.generated.related_topics
    : [];

  return createPopup({
    type: 'related-questions-topics',
    id: 'ai-related-questions-topics-popup',
    title: getLocalizedString('relatedQuestionsTopicsTitle') || 'Related Questions & Topics',
    contentGenerator: (popupBody, popup) => {
      // 添加相关问题部分
      if (relatedQuestions && relatedQuestions.length > 0) {
        const questionsLabel = document.createElement('div');
        questionsLabel.className = 'ai-prompt-optimizer-popup-label';
        questionsLabel.textContent = getLocalizedString('relatedQuestionsLabel') || 'Related Questions:';
        popupBody.appendChild(questionsLabel);

        const questionsList = document.createElement('ul');
        questionsList.className = 'ai-prompt-optimizer-popup-list questions-list';

        relatedQuestions.forEach(question => {
          const questionItem = document.createElement('li');
          questionItem.className = 'ai-prompt-optimizer-popup-list-item question-item';
          questionItem.textContent = question;

          // 添加点击事件，将问题填入输入框
          questionItem.addEventListener('click', () => {
            setInputText(platform, question);
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          });

          questionsList.appendChild(questionItem);
        });

        popupBody.appendChild(questionsList);
      }

      // 添加相关主题部分
      if (relatedTopics && relatedTopics.length > 0) {
        const topicsLabel = document.createElement('div');
        topicsLabel.className = 'ai-prompt-optimizer-popup-label';
        topicsLabel.textContent = getLocalizedString('relatedTopicsLabel') || 'Related Topics:';
        popupBody.appendChild(topicsLabel);

        const topicsList = document.createElement('ul');
        topicsList.className = 'ai-prompt-optimizer-popup-list topics-list';

        relatedTopics.forEach(topic => {
          const topicItem = document.createElement('li');
          topicItem.className = 'ai-prompt-optimizer-popup-list-item topic-item';
          topicItem.textContent = topic;

          // 添加点击事件，将主题填入输入框，添加"Tell me more about:"前缀
          topicItem.addEventListener('click', () => {
            // 获取本地化的"Tell me more about:"前缀
            const prefix = getLocalizedString('tellMeMoreAbout') || 'Tell me more about:';
            // 将前缀和主题组合后填入输入框
            setInputText(platform, `${prefix} ${topic}`);
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          });

          topicsList.appendChild(topicItem);
        });

        popupBody.appendChild(topicsList);
      }
    },
    buttons: [
      {
        type: 'cancel',
        text: getLocalizedString('cancelButtonText') || 'Close',
        onClick: (_, popup) => {
          const overlay = document.getElementById('ai-prompt-optimizer-overlay');
          closePopup(popup, overlay);
        }
      }
    ]
  });
}



// Position popup in the center of the screen
function positionPopup(popup) {
  if (!popup) return;

  // Set fixed position for the popup
  popup.style.position = 'fixed';

  // Center the popup in the viewport
  popup.style.top = '50%';
  popup.style.left = '50%';
  popup.style.transform = 'translate(-50%, -50%) scale(0.95)';

  // Clear any previously set positioning
  popup.style.bottom = 'auto';
  popup.style.right = 'auto';
}

// Intelligently position dropdown menu based on available screen space
function positionDropdownMenu(dropdown, button) {
  if (!dropdown || !button) return;

  // Get button and dropdown dimensions
  const buttonRect = button.getBoundingClientRect();
  const dropdownRect = dropdown.getBoundingClientRect();

  // Get viewport dimensions
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;

  // Calculate available space above and below the button
  const spaceAbove = buttonRect.top;
  const spaceBelow = viewportHeight - buttonRect.bottom;

  // Calculate available space to the left and right of the button
  const spaceLeft = buttonRect.left;
  const spaceRight = viewportWidth - buttonRect.right;

  // Dropdown dimensions (with some padding for safety)
  const dropdownHeight = dropdownRect.height || 150; // fallback height
  const dropdownWidth = dropdownRect.width || 200;   // fallback width
  const padding = 10; // Safety padding from screen edges

  // Set basic positioning
  dropdown.style.position = 'absolute';
  dropdown.style.zIndex = '10000';

  // Determine vertical position (above or below)
  // Default to showing above, only show below if not enough space above
  let showAbove = true;

  if (spaceAbove >= dropdownHeight + padding) {
    // Enough space above - show above (default preference)
    showAbove = true;
  } else if (spaceBelow >= dropdownHeight + padding) {
    // Not enough space above but enough below - show below
    showAbove = false;
  } else {
    // Not enough space in either direction - choose the side with more space
    showAbove = spaceAbove > spaceBelow;
  }

  if (showAbove) {
    // Position above the button
    dropdown.style.bottom = `${viewportHeight - buttonRect.top + 5}px`;
    dropdown.style.top = 'auto';
  } else {
    // Position below the button
    dropdown.style.top = `${buttonRect.bottom + 5}px`;
    dropdown.style.bottom = 'auto';
  }

  // Determine horizontal position
  let leftPosition = buttonRect.left;

  // Check if dropdown would overflow on the right
  if (leftPosition + dropdownWidth > viewportWidth - padding) {
    // Align to the right edge of the button instead
    leftPosition = buttonRect.right - dropdownWidth;
  }

  // Check if dropdown would overflow on the left
  if (leftPosition < padding) {
    leftPosition = padding;
  }

  dropdown.style.left = `${leftPosition}px`;
  dropdown.style.right = 'auto';

  // Add a class to indicate the positioning for potential CSS adjustments
  dropdown.classList.remove('dropdown-above', 'dropdown-below');
  dropdown.classList.add(showAbove ? 'dropdown-above' : 'dropdown-below');
}

// Show popup with overlay and animation
function showPopupWithOverlay(popup) {
  if (!popup) return;

  // Add a subtle animation for better UX
  popup.style.transition = 'opacity 0.3s, transform 0.3s';

  // 移除所有可能存在的遮罩层
  const existingOverlays = document.querySelectorAll('.ai-prompt-optimizer-overlay');
  existingOverlays.forEach(o => o.remove());

  // 创建新的遮罩层
  const overlay = document.createElement('div');
  overlay.id = 'ai-prompt-optimizer-overlay';
  overlay.className = 'ai-prompt-optimizer-overlay';
  overlay.style.position = 'fixed';
  overlay.style.top = '0';
  overlay.style.left = '0';
  overlay.style.width = '100%';
  overlay.style.height = '100%';
  overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
  overlay.style.zIndex = '9999';
  overlay.style.opacity = '0';
  overlay.style.transition = 'opacity 0.3s';

  // Insert overlay before popup in the DOM
  popup.parentNode.insertBefore(overlay, popup);

  // Move popup above overlay in z-index
  popup.style.zIndex = '10000';

  // Use setTimeout to ensure the animation runs after the popup is positioned
  setTimeout(() => {
    // Fade in popup
    popup.style.opacity = '1';
    popup.style.transform = 'translate(-50%, -50%) scale(1)';

    // Fade in overlay
    overlay.style.opacity = '1';
  }, 50);

  // Close popup when clicking outside
  overlay.addEventListener('click', (e) => {
    // Only close if clicking directly on the overlay, not on popup or its children
    if (e.target === overlay) {
      closePopup(popup, overlay);
    }
  });

  // Update cancel/close buttons to use the fade out animation
  const closeButtons = popup.querySelectorAll('.ai-prompt-optimizer-popup-button.cancel');
  closeButtons.forEach(button => {
    // Remove existing click listeners
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);

    // Add new click listener with animation
    newButton.addEventListener('click', () => {
      closePopup(popup, overlay);
    });
  });
}

// Helper function to close popup with animation
function closePopup(popup, overlay) {
  // 如果没有传入overlay，尝试查找
  if (!overlay) {
    overlay = document.getElementById('ai-prompt-optimizer-overlay');
  }

  // Fade out popup
  if (popup) {
    popup.style.opacity = '0';
    popup.style.transform = 'translate(-50%, -50%) scale(0.95)';
  }

  // Fade out overlay
  if (overlay) {
    overlay.style.opacity = '0';
  }

  // Remove after animation completes
  setTimeout(() => {
    if (popup) popup.remove();

    // 移除所有可能存在的遮罩层
    const allOverlays = document.querySelectorAll('.ai-prompt-optimizer-overlay');
    allOverlays.forEach(o => o.remove());
  }, 300);
}

// Helper function to show error popup
function showErrorPopup(errorCode) {
  const errorPopup = createErrorPopup(errorCode);
  document.body.appendChild(errorPopup);
  positionPopup(errorPopup);
  showPopupWithOverlay(errorPopup);
  return errorPopup;
}

// Create loading popup for showing operation progress
function createLoadingPopup(operationType) {
  // Get localized text based on operation type
  let titleText = '';
  let messageText = '';

  switch (operationType) {
    case 'optimizePrompt':
      titleText = getLocalizedString('optimizingPromptTitle') || 'Optimizing Prompt';
      messageText = getLocalizedString('optimizingPromptMessage') || 'Please wait while we optimize your prompt...';
      break;
    case 'question_optimize':
      titleText = getLocalizedString('optimizingQuestionTitle') || 'Optimizing Question';
      messageText = getLocalizedString('optimizingQuestionMessage') || 'Please wait while we optimize your question...';
      break;
    case 'prompt_detail_form':
      titleText = getLocalizedString('optimizingInstructionTitle') || 'Optimizing Instructions';
      messageText = getLocalizedString('optimizingInstructionMessage') || 'Please wait while we optimize your instructions...';
      break;
    case 'getRelatedQuestionsTopics':
      titleText = getLocalizedString('gettingRelatedTitle') || 'Getting Related Content';
      messageText = getLocalizedString('gettingRelatedMessage') || 'Please wait while we generate related questions and topics...';
      break;
    case 'getCriticalAnalysis':
      titleText = getLocalizedString('analyzingTitle') || 'Analyzing Content';
      messageText = getLocalizedString('analyzingMessage') || 'Please wait while we perform critical analysis...';
      break;
    default:
      titleText = getLocalizedString('processingTitle') || 'Processing';
      messageText = getLocalizedString('processingMessage') || 'Please wait while we process your request...';
      break;
  }

  return createPopup({
    type: 'loading',
    id: 'ai-loading-popup',
    title: titleText,
    contentGenerator: (popupBody) => {
      // Create loading container
      const loadingContainer = document.createElement('div');
      loadingContainer.className = 'ai-loading-popup-container';

      // Create loading spinner
      const loadingSpinner = document.createElement('div');
      loadingSpinner.className = 'ai-loading-popup-spinner';

      // Create loading message
      const loadingMessage = document.createElement('div');
      loadingMessage.className = 'ai-loading-popup-message';
      loadingMessage.textContent = messageText;

      loadingContainer.appendChild(loadingSpinner);
      loadingContainer.appendChild(loadingMessage);
      popupBody.appendChild(loadingContainer);
    },
    buttons: [] // No buttons for loading popup
  });
}

// Show loading popup
function showLoadingPopup(operationType) {
  // Remove any existing loading popup
  const existingLoadingPopup = document.getElementById('ai-loading-popup');
  if (existingLoadingPopup) {
    existingLoadingPopup.remove();
  }

  const loadingPopup = createLoadingPopup(operationType);
  document.body.appendChild(loadingPopup);
  positionPopup(loadingPopup);
  showPopupWithOverlay(loadingPopup);
  return loadingPopup;
}

// Hide loading popup
function hideLoadingPopup() {
  const loadingPopup = document.getElementById('ai-loading-popup');
  if (loadingPopup) {
    const overlay = document.getElementById('ai-prompt-optimizer-overlay');
    closePopup(loadingPopup, overlay);
  }
}


// Helper functions for handling specific actions
function openLoginPage() {
  window.open(`${APP_URL}/#/login?source=extension`);
}

function openPricingPage() {
  window.open(`${APP_URL}/#/aiplans`, '_blank');
}

// Create error message popup
function createErrorPopup(errorCode) {
  console.log('Processing error code:', errorCode);

  // 根据错误代码获取格式化的错误消息
  let formattedErrorMessage;
  let buttons = [];

  // 处理特定错误代码
  switch (errorCode) {
    case ERROR_CODES.EXCEED_FREE_TRIAL_QUOTA:
      formattedErrorMessage = getLocalizedString('errorExceedFreeTrialQuota') || 'You have exceeded your free trial quota.';
      // 添加价格按钮
      buttons = [
        {
          type: 'confirm',
          text: getLocalizedString('pricingButton') || 'View Plans',
          onClick: (_, popup) => {
            openPricingPage();
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;

    case ERROR_CODES.EXCEED_DAILY_QUOTA:
      formattedErrorMessage = getLocalizedString('errorExceedDailyQuota') || 'You have exceeded your daily quota.';
      // 添加价格按钮
      buttons = [
        {
          type: 'confirm',
          text: getLocalizedString('pricingButton') || 'View Plans',
          onClick: (_, popup) => {
            openPricingPage();
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;

    case ERROR_CODES.EXCEED_MSG_LIMIT:
      formattedErrorMessage = getLocalizedString('errorExceedMsgLimit') || 'You have exceeded the message limit.';
      // 添加价格按钮
      buttons = [
        {
          type: 'confirm',
          text: getLocalizedString('pricingButton') || 'View Plans',
          onClick: (_, popup) => {
            openPricingPage();
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;

    case ERROR_CODES.LOGIN_REQUIRED:
      formattedErrorMessage = getLocalizedString('errorLoginRequired') || 'Login is required to use this feature.';
      // 添加登录按钮
      buttons = [
        {
          type: 'confirm',
          text: getLocalizedString('loginFunBlocksButton') || 'Login FunBlocks AI Account',
          onClick: (_, popup) => {
            openLoginPage();
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        },
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;

    case ERROR_CODES.SERVICE_UNAVAILABLE:
      formattedErrorMessage = getLocalizedString('errorServiceUnavailable') || 'Service is currently unavailable. Please try again later.';
      // 默认关闭按钮
      buttons = [
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;

    case ERROR_CODES.GENERATION_FAILED:
      formattedErrorMessage = getLocalizedString('errorAIGenerationFailed') || 'AI generation failed. Please try again.';
      // 默认关闭按钮
      buttons = [
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;

    default:
      // 对于未知错误代码，显示通用错误消息
      formattedErrorMessage = getLocalizedString('errorGeneralError') || 'An error occurred. Please try again.';
      // 默认关闭按钮
      buttons = [
        {
          type: 'cancel',
          text: getLocalizedString('closeButtonText') || 'Close',
          onClick: (_, popup) => {
            const overlay = document.getElementById('ai-prompt-optimizer-overlay');
            closePopup(popup, overlay);
          }
        }
      ];
      break;
  }

  return createPopup({
    type: 'error',
    id: 'ai-error-popup',
    title: getLocalizedString('errorTitle') || 'Error',
    contentGenerator: (popupBody) => {
      // 创建错误信息容器
      const errorContainer = document.createElement('div');
      errorContainer.className = 'ai-prompt-optimizer-popup-error-container';

      // 创建错误图标
      const errorIcon = document.createElement('div');
      errorIcon.className = 'ai-prompt-optimizer-popup-error-icon';

      // 创建错误文本
      const errorText = document.createElement('div');
      errorText.className = 'ai-prompt-optimizer-popup-text error-text';
      errorText.textContent = formattedErrorMessage;

      // 将错误图标和文本添加到容器中
      errorContainer.appendChild(errorIcon);
      errorContainer.appendChild(errorText);
      popupBody.appendChild(errorContainer);
    },
    buttons: buttons
  });
}

// Create dropdown menu for critical thinking assistant button
function createCriticalThinkingAssistantDropdownMenu(platform, button, role) {
  // Remove any existing dropdown
  const existingDropdown = document.getElementById('ai-critical-thinking-assistant-dropdown');
  if (existingDropdown) {
    existingDropdown.remove();
  }

  // Create dropdown container
  const dropdown = document.createElement('div');
  dropdown.id = 'ai-critical-thinking-assistant-dropdown';
  dropdown.className = 'ai-prompt-optimizer-dropdown';

  // Create menu items based on role
  let menuItems = [];

  if (role === 'user') {
    // For user questions: Related Questions & Topics, Optimize Question, Optimize Instructions
    menuItems = [
      {
        text: getLocalizedString('relatedQuestionsTopicsButtonText') || '相关问题和主题',
        action: 'getRelatedQuestionsTopics'
      },
      {
        text: getLocalizedString('betterQuestionText') || '优化问题',
        action: 'optimizePrompt',
        app: 'question_optimize'
      },
      {
        text: getLocalizedString('optimizeInstructionText') || '优化指令',
        action: 'optimizePrompt',
        app: 'prompt_detail_form'
      }
    ];
  } else {
    // For AI responses: Related Questions & Topics, Critical Analysis
    menuItems = [
      {
        text: getLocalizedString('relatedQuestionsTopicsButtonText') || '相关问题和主题',
        action: 'getRelatedQuestionsTopics'
      },
      {
        text: getLocalizedString('criticalAnalysisText') || 'Critical Analysis',
        action: 'getCriticalAnalysis'
      }
    ];
  }

  menuItems.forEach(item => {
    const menuItem = document.createElement('div');
    menuItem.className = 'ai-prompt-optimizer-dropdown-item';
    menuItem.textContent = item.text;

    menuItem.addEventListener('click', (e) => {
      // Completely prevent event propagation and default behavior
      e.stopPropagation();
      e.preventDefault();
      e.stopImmediatePropagation();

      // Remove dropdown and handle the action
      dropdown.remove();

      // Also remove any click event listeners that were added to close the dropdown
      document.removeEventListener('click', window.currentRelatedDropdownCloseHandler);
      window.currentRelatedDropdownCloseHandler = null;

      // Handle the selected action
      handleCriticalThinkingAssistantAction(item, role, button);
    }, true);

    dropdown.appendChild(menuItem);
  });

  // Add dropdown to the DOM first so we can measure its dimensions
  document.body.appendChild(dropdown);

  // Position dropdown intelligently based on available space
  positionDropdownMenu(dropdown, button);

  // Prevent clicks on the button from immediately closing the dropdown
  button.addEventListener('click', (e) => {
    e.stopPropagation();
  }, { once: true });

  // Close dropdown when clicking outside
  const closeDropdown = (e) => {
    if (!dropdown.contains(e.target) && e.target !== button) {
      dropdown.remove();
      document.removeEventListener('click', closeDropdown);
      window.currentRelatedDropdownCloseHandler = null;
    }
  };

  // Store the closeDropdown function in a global variable so we can remove it later
  window.currentRelatedDropdownCloseHandler = closeDropdown;

  // Use setTimeout to avoid immediate triggering of the click event
  setTimeout(() => {
    document.addEventListener('click', closeDropdown);
  }, 0);

  return dropdown;
}

// Create dropdown menu for optimize button
function createOptimizeDropdownMenu(platform, button) {
  // Remove any existing dropdown
  const existingDropdown = document.getElementById('ai-prompt-optimizer-dropdown');
  if (existingDropdown) {
    existingDropdown.remove();
  }

  // Create dropdown container
  const dropdown = document.createElement('div');
  dropdown.id = 'ai-prompt-optimizer-dropdown';
  dropdown.className = 'ai-prompt-optimizer-dropdown';

  // Create menu items
  const menuItems = [
    {
      text: getLocalizedString('betterQuestionText') || '优化提问',
      app: 'question_optimize'
    },
    {
      text: getLocalizedString('optimizeInstructionText') || '优化指令',
      app: 'prompt_detail_form'
    }
  ];

  if (['chatgpt.com', 'gemini.google.com'].includes(platform)) {
    menuItems.push({
      text: getLocalizedString('optimizeImagePromptText') || '优化图像生成Prompt',
      app: 'prompt_image_form'
    })
  }

  menuItems.forEach(item => {
    const menuItem = document.createElement('div');
    menuItem.className = 'ai-prompt-optimizer-dropdown-item';
    menuItem.textContent = item.text;

    menuItem.addEventListener('click', (e) => {
      // Completely prevent event propagation and default behavior
      e.stopPropagation();
      e.preventDefault();
      e.stopImmediatePropagation();

      // Remove dropdown and handle the action
      dropdown.remove();

      // Also remove any click event listeners that were added to close the dropdown
      document.removeEventListener('click', window.currentDropdownCloseHandler);
      window.currentDropdownCloseHandler = null;

      const inputText = getInputText(getCurrentPlatform());
      const button = document.getElementById('ai-prompt-optimizer-button');

      // Handle the selected action
      handleOptimizeAction(item.app, inputText);
    }, true);

    dropdown.appendChild(menuItem);
  });

  // Add dropdown to the DOM first so we can measure its dimensions
  document.body.appendChild(dropdown);

  // Position dropdown intelligently based on available space
  positionDropdownMenu(dropdown, button);

  // Prevent clicks on the button from immediately closing the dropdown
  button.addEventListener('click', (e) => {
    e.stopPropagation();
  }, { once: true });

  // Close dropdown when clicking outside
  const closeDropdown = (e) => {
    if (!dropdown.contains(e.target) && e.target !== button) {
      dropdown.remove();
      document.removeEventListener('click', closeDropdown);
      window.currentDropdownCloseHandler = null;
    }
  };

  // Store the closeDropdown function in a global variable so we can remove it later
  window.currentDropdownCloseHandler = closeDropdown;

  // Use setTimeout to avoid immediate triggering of the click event
  setTimeout(() => {
    document.addEventListener('click', closeDropdown);
  }, 0);

  return dropdown;
}

// Handle optimize button click - show dropdown menu
function handleOptimizeClick(event) {
  // Prevent the default action and stop event propagation
  if (event) {
    event.preventDefault();
    event.stopPropagation();
  }

  const platform = getCurrentPlatform();
  if (!platform) return;

  const inputText = getInputText(platform);

  // Don't proceed if input is empty
  if (!inputText.trim()) {
    // Make sure button is disabled
    const button = document.getElementById('ai-prompt-optimizer-button');
    if (button) {
      button.disabled = true;
      button.classList.add('disabled');
    }
    return;
  }

  // Get the button
  const button = document.getElementById('ai-prompt-optimizer-button');

  // Check if dropdown already exists
  const existingDropdown = document.getElementById('ai-prompt-optimizer-dropdown');

  // If dropdown exists, remove it and return (toggle behavior)
  if (existingDropdown) {
    existingDropdown.remove();
    // Also remove any click event listeners that were added to close the dropdown
    document.removeEventListener('click', window.currentDropdownCloseHandler);
    window.currentDropdownCloseHandler = null;
    return;
  }

  // Otherwise, create and show the dropdown
  createOptimizeDropdownMenu(platform, button);
}

// Handle critical thinking assistant action based on selected menu item
function handleCriticalThinkingAssistantAction(item, role, button) {
  const platform = getCurrentPlatform();
  if (!platform) return;

  let messageContent = '';

  // First, check if we have saved selected text from when the button was clicked
  if (button.dataset.selectedText && button.dataset.selectedText.trim()) {
    messageContent = button.dataset.selectedText.trim();
    // Clear the saved text after using it
    delete button.dataset.selectedText;
  } else {
    // Fallback: try to get current selection (though this may be empty after dropdown display)
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      messageContent = selection.toString().trim();
    }
  }

  // If no selected text, try to get message content from the DOM
  if (!messageContent) {
    const config = PLATFORM_SELECTORS[platform];

    let parentContainer = button.parentNode;

    let messageContentElement;
    const selector = role === 'user' && config.querySelector ? config.querySelector : config.messageSelector;
    while (parentContainer && !messageContentElement) {
      if (parentContainer.nodeType === 1 && typeof parentContainer.matches === 'function' && parentContainer.matches(selector)) {
        messageContentElement = parentContainer;
      } else if (parentContainer.nodeType === 1 && typeof parentContainer.querySelector === 'function') {
        messageContentElement = parentContainer.querySelector(selector);
      }
      parentContainer = parentContainer.parentNode;
    }

    if (!messageContentElement && platform === 'chat.deepseek.com') {
      // 从button往上找父辈节点，直到找到一个包含有text节点的div子孙节点
      let ancestor = button.parentNode;
      while (ancestor && ancestor !== document.body) {
        // 查找ancestor下的所有div子孙节点
        const divs = ancestor.querySelectorAll('div');
        let found = false;
        divs.forEach(div => {
          // 检查div是否有非空的text节点
          for (let node of div.childNodes) {
            if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
              messageContentElement = div;
              found = true;
              break;
            }
          }
        });
        if (found) break;
        ancestor = ancestor.parentNode;
      }
    }

    if (!messageContentElement) return;

    messageContent = messageContentElement.innerText?.replace(getLocalizedString('relatedQuestionsTopicsButtonText'), '').trim();
  }

  if (!messageContent) return;

  if (item.action === 'optimizePrompt') {
    // Handle optimize prompt actions (for user questions)
    handleOptimizeAction(item.app, messageContent);
  } else if (item.action === 'getRelatedQuestionsTopics') {
    // Show loading popup for related questions and topics
    showLoadingPopup('getRelatedQuestionsTopics');

    // Handle related questions and topics action
    chrome.runtime.sendMessage(
      { action: 'getRelatedQuestionsTopics', messageContent },
      (response) => {
        // Hide loading popup
        hideLoadingPopup();

        if (response && response.success) {
          // Parse the JSON response
          const jsonResult = extractJSONFromString(response.relatedData);

          if (jsonResult) {
            // Create and show popup with related questions and topics data
            const popupData = jsonResult;
            const popup = createRelatedQuestionsTopicsPopup(popupData, platform);

            // Make sure any existing popups are removed first
            const existingPopup = document.getElementById('ai-related-questions-topics-popup');
            if (existingPopup) {
              existingPopup.remove();
            }

            // Add the popup to the DOM
            document.body.appendChild(popup);

            // Position the popup in the center of the screen
            positionPopup(popup);
            showPopupWithOverlay(popup);
          } else {
            console.error('Failed to parse related questions and topics JSON:', response.relatedData);
            showErrorPopup(ERROR_CODES.GENERATION_FAILED);
          }
        } else {
          console.error('Failed to get related questions and topics:', response?.errorCode || 'Unknown error');
          showErrorPopup(response?.errorCode || ERROR_CODES.GENERATION_FAILED);
        }
      }
    );
  } else if (item.action === 'getCriticalAnalysis') {
    // Show loading popup for critical analysis
    showLoadingPopup('getCriticalAnalysis');

    // Handle critical analysis action
    chrome.runtime.sendMessage(
      { action: 'getCriticalAnalysis', messageContent },
      (response) => {
        // Hide loading popup
        hideLoadingPopup();

        if (response && response.success) {
          // Create and show popup with critical analysis data
          const popup = createCriticalAnalysisPopup(response.analysisData, platform);

          // Make sure any existing popups are removed first
          const existingPopup = document.getElementById('ai-critical-analysis-popup');
          if (existingPopup) {
            existingPopup.remove();
          }

          // Add the popup to the DOM
          document.body.appendChild(popup);

          // Position the popup in the center of the screen
          positionPopup(popup);
          showPopupWithOverlay(popup);
        } else {
          console.error('Failed to get critical analysis:', response?.errorCode || 'Unknown error');
          showErrorPopup(response?.errorCode || ERROR_CODES.GENERATION_FAILED);
        }
      }
    );
  }
}

// Handle optimize action based on selected menu item
function handleOptimizeAction(app, inputText) {

  // Don't proceed if input is empty
  if (!inputText.trim()) return;

  const platform = getCurrentPlatform();

  // Show loading popup instead of button loading state
  showLoadingPopup(app);

  // Send message to background script with the specific app parameter
  chrome.runtime.sendMessage(
    { action: 'optimizePrompt', prompt: inputText, app: app },
    (response) => {
      // Hide loading popup
      hideLoadingPopup();

      if (response && response.success && response.optimizedPrompt) {
        console.log('optimized prompt response:', response.optimizedPrompt);

        const jsonResult = extractJSONFromString(response.optimizedPrompt);

        // Check if the extracted JSON is valid
        if (!jsonResult || !jsonResult.generated) {
          console.error('Failed to extract valid JSON from response:', response.optimizedPrompt);
          showErrorPopup(ERROR_CODES.GENERATION_FAILED);
          return;
        }

        // Handle different response types based on the app
        if (app === 'question_optimize') {
          handleQuestionOptimizeResponse(jsonResult, platform);
        } else if (['prompt_detail_form', 'prompt_image_form'].includes(app)) {
          handlePromptDetailFormResponse(jsonResult, platform, app);
        } else {
          // Fallback to original behavior for backward compatibility
          const popup = createOptimizedPromptPopup(jsonResult, platform);
          document.body.appendChild(popup);
          positionPopup(popup);
          showPopupWithOverlay(popup);
        }
      } else {
        console.error('Failed to optimize prompt:', response?.errorCode || 'Unknown error');
        showErrorPopup(response?.errorCode || ERROR_CODES.GENERATION_FAILED);
      }
    }
  );
}

// Handle critical thinking assistant button click - show dropdown menu
function handleCriticalThinkingAssistantClick(role, messageContainerElement) {
  const platform = getCurrentPlatform();
  if (!platform) return;

  const button = document.activeElement;

  // Check if dropdown already exists
  const existingDropdown = document.getElementById('ai-critical-thinking-assistant-dropdown');

  // If dropdown exists, remove it and return (toggle behavior)
  if (existingDropdown) {
    existingDropdown.remove();
    // Also remove any click event listeners that were added to close the dropdown
    document.removeEventListener('click', window.currentRelatedDropdownCloseHandler);
    window.currentRelatedDropdownCloseHandler = null;
    return;
  }

  // Capture selected text before showing dropdown (as dropdown display may clear selection)
  let selectedText = '';
  const selection = window.getSelection();
  if (selection && selection.toString().trim()) {
    selectedText = selection.toString().trim();
  }

  // Store the selected text on the button for later use
  button.dataset.selectedText = selectedText;

  // Otherwise, create and show the dropdown
  createCriticalThinkingAssistantDropdownMenu(platform, button, role);
}

// Insert critical thinking assistant button into message action bar
function insertCriticalThinkingAssistantButton(platform, role, messageElement) {
  // Check if the message already has a critical thinking assistant button
  if (messageElement.querySelector('#ai-critical-thinking-assistant-button')) return;

  const config = PLATFORM_SELECTORS[platform];
  let insertPosition;

  // Find the message actions container
  let actionsContainer = messageElement.querySelector(role === 'user' && config.queryActionsSelector ? config.queryActionsSelector : config.messageActionsSelector);

  if (!actionsContainer && platform === 'gemini.google.com') {
    actionsContainer = messageElement.querySelector('.query-content');
    insertPosition = 'afterbegin';
  }

  if (!actionsContainer) return;

  // Check if any ancestor node is a fieldset
  let parentContainer = actionsContainer;
  while (parentContainer) {
    if (parentContainer.tagName?.toLowerCase() === 'fieldset') {
      return;
    }

    parentContainer = parentContainer.parentNode;
  }

  // Create the button
  const button = createCriticalThinkingAssistantButton();

  // Add click event listener with explicit function reference to ensure proper binding
  const clickHandler = function (e) {
    e.stopPropagation();
    e.preventDefault();
    handleCriticalThinkingAssistantClick(role, messageElement);
  };

  button.addEventListener('click', clickHandler);

  if (platform === 'gemini.google.com' && role === 'model') {
    // actionsContainer.childNodes[0].appendChild(button);
    if (actionsContainer.childNodes[0]?.childNodes[0]) {
      actionsContainer.childNodes[0].childNodes[0].insertAdjacentElement('afterbegin', button);
    } else {
      actionsContainer.childNodes[0].appendChild(button);
    }
  } else if (platform === 'chatgpt.com') {
    let parent = actionsContainer.parentElement;
    while (parent && parent.childNodes.length <= 1) {
      parent = parent.parentElement;
    }
    if (parent) {
      parent.insertAdjacentElement('beforebegin', button);
    }
  } else {
    // Insert the button
    if (!insertPosition) {
      insertPosition = role === 'user' ? config.queryActionsInsertPosition : config.messageActionsInsertPosition;
    }

    if (insertPosition === 'afterend') {
      actionsContainer.insertAdjacentElement('afterend', button);
    } else if (insertPosition === 'beforebegin') {
      actionsContainer.insertAdjacentElement('beforebegin', button);
    } else if (insertPosition === 'beforeend') {
      actionsContainer.appendChild(button);
    } else if (insertPosition === 'afterbegin') {
      actionsContainer.insertAdjacentElement('afterbegin', button);
    }
  }

}

function findMessageElements(element) {
  if (!element) return null;
  let firstChild = element.firstElementChild;
  if (firstChild) {
    const otherChildren = Array.from(element.children).slice(1);
    if (otherChildren.some(child => child.querySelector('#chat-input'))) {
      return firstChild.childNodes;
    } else {
      return findMessageElements(firstChild);
    }
  }
  return null;
}

// Add critical thinking assistant buttons to all messages
function addCriticalThinkingAssistantButtons(platform) {
  const config = PLATFORM_SELECTORS[platform];

  // Find all message elements
  let messageElements;

  if (platform === 'chat.deepseek.com') {
    const scrollable = document.querySelectorAll('.scrollable')[1];
    messageElements = findMessageElements(scrollable);
  } else {
    messageElements = document.querySelectorAll(config.messageSelector);
  }

  if (!messageElements) return;

  // Add button to each message
  messageElements.forEach(messageElement => {
    if (messageElement.tagName.toLowerCase() === 'svg') return;

    if (platform === 'chat.deepseek.com') {
      insertCriticalThinkingAssistantButton(platform, messageElement.querySelector(config.messageSelector) ? 'model' : 'user', messageElement);
      return;
    }

    let role = 'model';

    if (messageElement.getAttribute('data-message-author-role') == 'user') {
      role = 'user'
    }

    // Find the closest parent that contains both the message content and actions
    let parentElement = messageElement;
    while (parentElement && !parentElement.querySelector(config.messageActionsSelector)) {
      parentElement = parentElement.parentElement;
    }

    if (parentElement) {
      insertCriticalThinkingAssistantButton(platform, role, parentElement);
    }
  });


  if (config.querySelector && platform !== 'chat.deepseek.com') {
    const queryElements = document.querySelectorAll(config.querySelector);

    // Add button to each message
    queryElements.forEach(messageElement => {
      // Find the closest parent that contains both the message content and actions
      let parentElement = messageElement;

      while (parentElement && !parentElement.querySelector(config.queryActionsSelector)) {
        parentElement = parentElement.parentElement;
      }

      if (parentElement) {
        insertCriticalThinkingAssistantButton(platform, 'user', parentElement);
      }
    });
  }


  const buttonContainer = platform === 'perplexity.ai' && document.querySelector('div.absolute[class*="z-\\["]:has(button)')
    || platform === 'chatgpt.com' && document.querySelector('.btn.btn-small.btn-secondary');
  if (buttonContainer) {
    // insertCriticalThinkingAssistantButton(platform, 'user', parentElement);

    if (buttonContainer.parentNode.querySelector('#ai-critical-thinking-assistant-button')) return;

    const button = createCriticalThinkingAssistantButton();

    // Add click event listener with explicit function reference to ensure proper binding
    const clickHandler = function (e) {
      e.stopPropagation();
      e.preventDefault();
      handleCriticalThinkingAssistantClick('selection', null);
    };

    button.addEventListener('click', clickHandler);
    if (platform === 'perplexity.ai') {
      let container = buttonContainer.querySelector('button')?.parentNode;
      if (container) {
        container.style.display = 'flex';
        container.style.flexDirection = 'row';
        container.style.alignItems = 'center';
        container.insertAdjacentElement('afterbegin', button);
      }
    } else if (platform === 'chatgpt.com') {
      buttonContainer.insertAdjacentElement('afterbegin', button);
    }
  }
}

// Update optimize button state based on input field content
function updateOptimizeButtonState(platform) {
  const button = document.getElementById('ai-prompt-optimizer-button');
  if (!button) return;

  const inputText = getInputText(platform);

  // Disable button if input is empty, enable if it has content
  if (!inputText.trim()) {
    button.disabled = true;
    button.classList.add('disabled');
  } else {
    button.disabled = false;
    button.classList.remove('disabled');
  }
}

// Set up monitoring for input field changes
function setupInputFieldMonitoring(platform) {
  const config = PLATFORM_SELECTORS[platform];
  const inputSelector = config.inputSelector;
  const inputElement = document.querySelector(inputSelector);

  if (!inputElement) return;

  // For textarea and input elements
  if (inputElement.tagName.toLowerCase() === 'textarea' || inputElement.tagName.toLowerCase() === 'input') {
    // Listen for input events
    inputElement.addEventListener('input', () => {
      updateOptimizeButtonState(platform);
    });
  }
  // For contenteditable elements
  else if (inputElement.getAttribute('contenteditable') === 'true') {
    // Set up a mutation observer to monitor content changes
    const inputObserver = new MutationObserver(() => {
      updateOptimizeButtonState(platform);
    });

    // Observe changes to the input element's content
    inputObserver.observe(inputElement, {
      childList: true,
      characterData: true,
      subtree: true
    });
  }
}



// Initialize the extension
function initialize() {
  const platform = getCurrentPlatform();
  if (!platform) return;

  // Insert optimize button initially
  insertButton(platform);

  // Add critical thinking assistant buttons to existing messages
  addCriticalThinkingAssistantButtons(platform);

  // Set up a mutation observer to handle dynamic content changes
  const observer = new MutationObserver(() => {
    // Check if optimize button needs to be inserted
    if (!document.getElementById('ai-prompt-optimizer-button')) {
      insertButton(platform);
    }

    // Check for new messages and add critical thinking assistant buttons
    addCriticalThinkingAssistantButtons(platform);
  });

  // For other platforms, observe the entire body
  observer.observe(document.body, { childList: true, subtree: true });
}

// Initialize when the DOM is fully loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', async () => {
    await initLocalization();
    initialize();
  });
} else {
  (async () => {
    await initLocalization();
    initialize();
  })();
}
