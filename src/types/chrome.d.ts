// Type definitions for Chrome extension API
declare namespace chrome {
  namespace runtime {
    function sendMessage<T = any>(
      message: any,
      responseCallback?: (response: T) => void
    ): void;

    function getURL(path: string): string;

    function openOptionsPage(callback?: () => void): void;

    const lastError: chrome.runtime.LastError | undefined;

    interface LastError {
      message?: string;
    }

    const onMessage: {
      addListener(
        callback: (
          message: any,
          sender: chrome.runtime.MessageSender,
          sendResponse: (response?: any) => void
        ) => void | boolean
      ): void;
    };

    interface MessageSender {
      tab?: chrome.tabs.Tab;
      frameId?: number;
      id?: string;
      url?: string;
      tlsChannelId?: string;
    }
  }

  namespace storage {
    interface StorageChange {
      oldValue?: any;
      newValue?: any;
    }

    interface StorageArea {
      get(
        keys: string | string[] | null | Record<string, any>,
        callback?: (items: Record<string, any>) => void
      ): void;

      set(
        items: Record<string, any>,
        callback?: () => void
      ): void;
    }

    const local: StorageArea;
    const sync: StorageArea;

    const onChanged: {
      addListener(
        callback: (
          changes: { [key: string]: StorageChange },
          areaName: string
        ) => void
      ): void;
      removeListener(
        callback: (
          changes: { [key: string]: StorageChange },
          areaName: string
        ) => void
      ): void;
    };
  }

  namespace i18n {
    function getMessage(messageName: string, substitutions?: string | string[]): string;
    function getUILanguage(): string;
  }

  namespace tabs {
    interface Tab {
      id?: number;
      url?: string;
      title?: string;
      favIconUrl?: string;
      status?: string;
      active: boolean;
      highlighted: boolean;
      pinned: boolean;
      audible?: boolean;
      discarded: boolean;
      autoDiscardable: boolean;
      mutedInfo?: {
        muted: boolean;
      };
      width?: number;
      height?: number;
      sessionId?: string;
    }

    interface CreateProperties {
      active?: boolean;
      index?: number;
      openerTabId?: number;
      pinned?: boolean;
      url?: string;
      windowId?: number;
    }

    function create(createProperties: CreateProperties, callback?: (tab: Tab) => void): void;
  }
}
