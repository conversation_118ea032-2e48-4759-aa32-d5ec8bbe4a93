import { useState, useEffect, useCallback } from 'react';
import LocalizationManager from '../../utils/LocalizationManager';

export const useI18n = () => {
  const [currentLanguage, setCurrentLanguage] = useState<string>('en');
  const [isLoaded, setIsLoaded] = useState<boolean>(false);

  // Initialize localization manager
  useEffect(() => {
    const initLocalization = async () => {
      const localizationManager = LocalizationManager.getInstance();
      await localizationManager.initialize();

      // Get current UI language from storage (with fallback to legacy language setting)
      chrome.storage.local.get(['uiLanguage', 'language'], async (result) => {
        const language = result.uiLanguage || result.language || 'en';
        await localizationManager.setLocale(language);
        setCurrentLanguage(language);
        setIsLoaded(true);
      });
    };

    initLocalization();
  }, []);

  // Update language when it changes
  useEffect(() => {
    if (!isLoaded) return;

    const updateLanguage = async () => {
      const localizationManager = LocalizationManager.getInstance();
      await localizationManager.setLocale(currentLanguage);
    };

    updateLanguage();
  }, [currentLanguage, isLoaded]);

  // Listen for storage changes
  useEffect(() => {
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }, areaName: string) => {
      if (areaName === 'local' && (changes.uiLanguage || changes.language)) {
        // Prefer uiLanguage, fallback to language for backward compatibility
        const newLanguage = changes.uiLanguage?.newValue || changes.language?.newValue;
        if (newLanguage) {
          setCurrentLanguage(newLanguage);
        }
      }
    };

    chrome.storage.onChanged.addListener(handleStorageChange);
    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
    };
  }, []);

  // Get message function
  const getMessage = useCallback((key: string): string => {
    const localizationManager = LocalizationManager.getInstance();
    return localizationManager.getMessage(key);
  }, []);

  return {
    getMessage,
    currentLanguage,
    isLoaded
  };
};

export default useI18n;
