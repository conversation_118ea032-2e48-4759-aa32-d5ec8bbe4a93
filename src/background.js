// Background service worker for AI Prompt Optimizer

import { API_URL } from "./utils/Constants";
import { ERROR_CODES } from "./utils/ErrorCode";
import { getUILanguageApiValue, getAIOutputLanguageApiValue } from "./utils/LanguageConfig";

// Default settings
const DEFAULT_SETTINGS = {
  uiLanguage: 'en',
  aiOutputLanguage: 'en',
  // Keep legacy language field for backward compatibility
  language: 'en'
};

// Helper function to inject content script and CSS into a tab
async function injectContentScriptIntoTab(tabId, tabUrl) {
  try {
    // Inject CSS first
    await chrome.scripting.insertCSS({
      target: { tabId },
      files: ['content.css']
    });

    // Then inject the content script
    await chrome.scripting.executeScript({
      target: { tabId },
      files: ['content.js']
    });

    console.log(`Successfully injected content script into tab: ${tabUrl}`);
    return true;
  } catch (error) {
    // Handle specific error cases
    if (error.message.includes('cannot access contents of url')) {
      console.warn(`Cannot inject into tab ${tabUrl} - restricted access`);
    } else if (error.message.includes('The tab was closed')) {
      console.warn(`Cannot inject into tab ${tabId} - tab was closed`);
    } else {
      console.error(`Error injecting into tab ${tabUrl}:`, error);
    }
    return false;
  }
}

// Initialize settings and inject content scripts into existing tabs
chrome.runtime.onInstalled.addListener(() => {
  // Initialize settings with migration support
  chrome.storage.local.get(['language', 'uiLanguage', 'aiOutputLanguage'], (result) => {
    const legacyLanguage = result.language || DEFAULT_SETTINGS.language;
    const settings = {
      uiLanguage: result.uiLanguage || legacyLanguage,
      aiOutputLanguage: result.aiOutputLanguage || legacyLanguage,
      // Keep legacy language field for backward compatibility
      language: legacyLanguage
    };
    chrome.storage.local.set(settings);
  });

  // Get the list of supported websites from the manifest
  const supportedWebsites = chrome.runtime.getManifest().content_scripts[0].matches;

  try {
    // Query for all tabs that match the supported websites
    chrome.tabs.query({ url: supportedWebsites }, (tabs) => {
      if (tabs.length === 0) {
        console.log('No matching tabs found for auto-injection');
        return;
      }

      console.log(`Found ${tabs.length} matching tabs for auto-injection`);

      // Inject content script and CSS into each matching tab
      tabs.forEach(async (tab) => {
        // Skip injection for tabs that don't have a valid ID or are not in a ready state
        if (!tab.id || tab.status !== 'complete') {
          console.log(`Skipping tab ${tab.url} - not ready or invalid ID`);
          return;
        }

        // Use the helper function to inject content script and CSS
        await injectContentScriptIntoTab(tab.id, tab.url);
      });
    });

    // Set up a listener for tab updates to inject the content script when a tab is updated
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      // Only inject when the tab is fully loaded and the URL matches our patterns
      if (changeInfo.status === 'complete' && tab.url) {
        // Check if the tab URL matches any of our supported websites
        const isSupported = supportedWebsites.some(pattern => {
          // Extract the hostname from the pattern (e.g., "https://chatgpt.com/*" -> "chatgpt.com")
          const patternHostname = pattern.replace(/^https?:\/\//, '').replace(/\/\*$/, '');

          // Check if the tab URL contains this hostname
          return tab.url.includes(patternHostname);
        });

        if (isSupported) {
          console.log(`Tab updated, injecting content script into: ${tab.url}`);

          // Use the helper function to inject content script and CSS
          injectContentScriptIntoTab(tabId, tab.url).catch(error => {
            console.warn(`Failed to inject into tab ${tab.url} during update:`, error);
          });
        }
      }
    });
  } catch (error) {
    console.error('Error during auto-injection process:', error);
  }
});

// Check login status
async function checkLoginStatus() {
  try {
    const response = await fetch(`${API_URL}/users/login-status`, {
      method: 'GET',
      credentials: 'include'
    });

    if (!response.ok) {
      return { isLoggedIn: false };
    }

    const data = await response.json();
    return {
      isLoggedIn: data.isLoggedIn || false,
      username: data.username || '',
      email: data.email || ''
    };
  } catch (error) {
    console.error('Error checking login status:', error);
    return { isLoggedIn: false };
  }
}

// Get remaining daily AI query quotas
async function getRemainDailyAIQueryQuotas() {
  try {
    const response = await fetch(`${API_URL}/ai/getRemainDailyAIQueryQuotas`, {
      method: 'GET',
      credentials: 'include'
    });

    if (!response.ok) {
      if (response.status === 403) {
        const error = new Error('Login required');
        error.code = ERROR_CODES.LOGIN_REQUIRED;
        throw error;
      }
      const error = new Error('Failed to fetch quota information');
      error.code = ERROR_CODES.SERVICE_UNAVAILABLE;
      throw error;
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error getting quota information:', error);
    if (error.code) {
      throw error;
    } else {
      const newError = new Error(error.message || 'Unknown error');
      newError.code = ERROR_CODES.SERVICE_UNAVAILABLE;
      throw newError;
    }
  }
}

// Handle messages from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'optimizePrompt') {
    // Use the app parameter from the message, or default to 'prompt_optimize'
    const app = message.app || 'prompt_detail_form';
    callAI(app, message.prompt)
      .then(optimizedPrompt => {
        sendResponse({ success: true, optimizedPrompt });
      })
      .catch(error => {
        console.error('Error optimizing prompt:', error);
        sendResponse({ success: false, errorCode: error.code || ERROR_CODES.GENERATION_FAILED });
      });
    return true; // Indicates we will send a response asynchronously
  } else if (message.action === 'getRelatedQuestionsTopics') {
    callAI('related_questions_topics', message.messageContent)
      .then(relatedData => {
        sendResponse({ success: true, relatedData });
      })
      .catch(error => {
        console.error('Error getting related questions and topics:', error);
        sendResponse({ success: false, errorCode: error.code || ERROR_CODES.GENERATION_FAILED });
      });
    return true; // Indicates we will send a response asynchronously
  } else if (message.action === 'getCriticalAnalysis') {
    callAI('critical_analysis', message.messageContent)
      .then(analysisData => {
        sendResponse({ success: true, analysisData });
      })
      .catch(error => {
        console.error('Error getting critical analysis:', error);
        sendResponse({ success: false, errorCode: error.code || ERROR_CODES.GENERATION_FAILED });
      });
    return true; // Indicates we will send a response asynchronously
  } else if (message.action === 'getSettings') {
    chrome.storage.local.get(['apiUrl', 'language', 'uiLanguage', 'aiOutputLanguage'], (result) => {
      const legacyLanguage = result.language || DEFAULT_SETTINGS.language;
      sendResponse({
        apiUrl: result.apiUrl || DEFAULT_SETTINGS.apiUrl,
        uiLanguage: result.uiLanguage || legacyLanguage,
        aiOutputLanguage: result.aiOutputLanguage || legacyLanguage,
        // Keep legacy language field for backward compatibility
        language: legacyLanguage
      });
    });
    return true; // Indicates we will send a response asynchronously
  } else if (message.action === 'saveSettings') {
    const settingsToSave = {
      apiUrl: message.settings.apiUrl
    };

    // Handle new separated language settings
    if (message.settings.uiLanguage) {
      settingsToSave.uiLanguage = message.settings.uiLanguage;
    }
    if (message.settings.aiOutputLanguage) {
      settingsToSave.aiOutputLanguage = message.settings.aiOutputLanguage;
    }

    // Keep legacy language field for backward compatibility
    if (message.settings.language) {
      settingsToSave.language = message.settings.language;
    }

    chrome.storage.local.set(settingsToSave, () => {
      sendResponse({ success: true });
    });
    return true; // Indicates we will send a response asynchronously
  } else if (message.action === 'checkLoginStatus') {
    checkLoginStatus()
      .then(status => {
        sendResponse(status);
      })
      .catch(error => {
        console.error('Error checking login status:', error);
        sendResponse({ isLoggedIn: false, errorCode: ERROR_CODES.SERVICE_UNAVAILABLE });
      });
    return true; // Indicates we will send a response asynchronously
  } else if (message.action === 'getRemainDailyAIQueryQuotas') {
    getRemainDailyAIQueryQuotas()
      .then(quotaData => {
        sendResponse({ success: true, quotaData });
      })
      .catch(error => {
        console.error('Error getting quota information:', error);
        sendResponse({ success: false, errorCode: error.code || ERROR_CODES.SERVICE_UNAVAILABLE });
      });
    return true; // Indicates we will send a response asynchronously
  } else if (message.action === 'injectContentScript') {
    // Handle request to inject content script into a specific tab
    if (sender.tab && sender.tab.id) {
      // Use the helper function to inject content script and CSS
      injectContentScriptIntoTab(sender.tab.id, sender.tab.url)
        .then(success => {
          sendResponse({ success });
        })
        .catch(error => {
          console.error('Error injecting content script:', error);
          sendResponse({ success: false, error: error.message });
        });
      return true; // Indicates we will send a response asynchronously
    } else {
      sendResponse({ success: false, error: 'Invalid tab ID' });
      return true;
    }
  }
});

async function callAI(action, input) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 180000);

    const { aiOutputLanguage, language } = await new Promise(resolve => {
      chrome.storage.local.get(['aiOutputLanguage', 'language'], resolve);
    });

    // Use aiOutputLanguage if available, fallback to legacy language setting
    const outputLanguage = aiOutputLanguage || language || 'en';

    const endpoint = `/ai/aiAction`;
    let data = {
      app: action,
      lang: getAIOutputLanguageApiValue(outputLanguage), // Convert AI output language value to API value
      service: 'flow',
      objType: 'flow_background',
      content: input,
    };

    const response = await fetch(`${API_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data
      }),
      credentials: 'include',
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.status === 403) {
      const error = new Error('Login required');
      error.code = ERROR_CODES.LOGIN_REQUIRED;
      throw error;
    }

    if (!response.ok) {
      const error = new Error('Failed to request ai service');
      error.code = ERROR_CODES.SERVICE_UNAVAILABLE;
      throw error;
    }

    const result = await response.json();

    if (result?.data && !result.data.err) {
      let data = result.data;

      return data?.content;
    }

    if (result?.data?.err === 'exceed_msg_limit') {
      const error = new Error('Exceeded message limit');
      error.code = ERROR_CODES.EXCEED_MSG_LIMIT;
      throw error;
    }

    const error = new Error('Failed to generate content');
    error.code = ERROR_CODES.GENERATION_FAILED;
    throw error;

  } catch (error) {
    // 保留原始错误代码，如果有的话
    if (error.code) {
      throw error;
    } else {
      const newError = new Error(error.message || 'Unknown error');
      newError.code = ERROR_CODES.SERVICE_UNAVAILABLE;
      throw newError;
    }
  } finally {
    // setIsLoading(false);
  }
}
