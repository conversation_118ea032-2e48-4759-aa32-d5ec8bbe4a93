// Localization utilities for content script

// Cache for localized messages
let messagesCache = {
  en: {},
  zh: {}
};

let currentLocale = 'en';
let isInitialized = false;

// Initialize localization
export async function initLocalization() {
  if (isInitialized) return;
  
  try {
    // Get current UI language from storage (with fallback to legacy language setting)
    const result = await new Promise((resolve) => {
      chrome.storage.local.get(['uiLanguage', 'language'], resolve);
    });

    currentLocale = result.uiLanguage || result.language || 'en';
    
    // Load messages for current locale
    await loadMessages(currentLocale);
    
    // Also load default locale messages as fallback
    if (currentLocale !== 'en') {
      await loadMessages('en');
    }
    
    // Listen for storage changes
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === 'local' && (changes.uiLanguage || changes.language)) {
        // Prefer uiLanguage, fallback to language for backward compatibility
        const newLanguage = changes.uiLanguage?.newValue || changes.language?.newValue;
        if (newLanguage && newLanguage !== currentLocale) {
          currentLocale = newLanguage;
          loadMessages(currentLocale).then(() => {
            // Refresh UI elements with new language
            refreshUI();
          });
        }
      }
    });
    
    isInitialized = true;
  } catch (error) {
    console.error('Error initializing localization:', error);
    // Load default locale as fallback
    await loadMessages('en');
    isInitialized = true;
  }
}

// Load messages for a specific locale
async function loadMessages(locale) {
  try {
    // First try to use Chrome's built-in i18n API
    const appName = chrome.i18n.getMessage('appName');
    if (appName && locale === chrome.i18n.getUILanguage()) {
      // Chrome's i18n API is working and matches our locale, no need to load manually
      return;
    }
    
    // If Chrome's i18n API doesn't work or doesn't match our locale, load manually
    const response = await fetch(chrome.runtime.getURL(`_locales/${locale}/messages.json`));
    if (!response.ok) {
      throw new Error(`Failed to load messages for ${locale}`);
    }
    
    const messagesData = await response.json();
    
    // Process messages into a simpler format
    const processedMessages = {};
    for (const key in messagesData) {
      processedMessages[key] = messagesData[key].message;
    }
    
    messagesCache[locale] = processedMessages;
  } catch (error) {
    console.error(`Error loading messages for ${locale}:`, error);
  }
}

// Get a localized message
export function getLocalizedString(key) {
  // Try to get message from current locale
  if (messagesCache[currentLocale] && messagesCache[currentLocale][key]) {
    return messagesCache[currentLocale][key];
  }
  
  // Try Chrome's built-in i18n API
  const chromeMessage = chrome.i18n.getMessage(key);
  if (chromeMessage) {
    return chromeMessage;
  }
  
  // Fall back to default locale
  if (currentLocale !== 'en' && messagesCache['en'] && messagesCache['en'][key]) {
    return messagesCache['en'][key];
  }
  
  // Return key as fallback
  return key;
}

// Refresh UI elements with new language
function refreshUI() {
  // This function will be called when the language changes
  // It should update all UI elements with the new language
  
  // Find and update all tooltips
  const tooltips = document.querySelectorAll('.tooltip-text');
  tooltips.forEach(tooltip => {
    const button = tooltip.parentElement;
    if (button) {
      const ariaLabel = button.getAttribute('aria-label');
      if (ariaLabel) {
        // Try to find the key based on the previous value
        const key = findKeyByValue(ariaLabel);
        if (key) {
          const newValue = getLocalizedString(key);
          tooltip.textContent = newValue;
          button.setAttribute('aria-label', newValue);
        }
      }
    }
  });
  
  // Find and update all popup titles and labels
  const popupLabels = document.querySelectorAll('.ai-prompt-optimizer-popup-label');
  popupLabels.forEach(label => {
    const key = findKeyByValue(label.textContent);
    if (key) {
      label.textContent = getLocalizedString(key);
    }
  });
  
  // Find and update all buttons
  const buttons = document.querySelectorAll('.ai-prompt-optimizer-popup-button');
  buttons.forEach(button => {
    const key = findKeyByValue(button.textContent);
    if (key) {
      button.textContent = getLocalizedString(key);
    }
  });
}

// Helper function to find a key by its value
function findKeyByValue(value) {
  // Common keys to check first
  const commonKeys = [
    'optimizeButtonText',
    'relatedQuestionsTopicsButtonText',
    'confirmButtonText',
    'cancelButtonText',
    'closeButtonText',
    'requirementsLabel',
    'promptLabel',
    'relatedQuestionsLabel',
    'relatedTopicsLabel',
    'optimizedPromptTitle',
    'relatedQuestionsTopicsTitle',
    'errorTitle'
  ];
  
  // Check common keys first
  for (const key of commonKeys) {
    if (messagesCache[currentLocale] && messagesCache[currentLocale][key] === value) {
      return key;
    }
    if (messagesCache['en'] && messagesCache['en'][key] === value) {
      return key;
    }
  }
  
  // Check all keys in current locale
  for (const key in messagesCache[currentLocale]) {
    if (messagesCache[currentLocale][key] === value) {
      return key;
    }
  }
  
  // Check all keys in default locale
  for (const key in messagesCache['en']) {
    if (messagesCache['en'][key] === value) {
      return key;
    }
  }
  
  return null;
}
