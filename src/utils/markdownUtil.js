/**
 * Markdown to HTML converter using the marked library
 */

import { marked } from 'marked';

/**
 * Configure marked with safe options for content script usage
 */
marked.setOptions({
  breaks: true,        // Convert line breaks to <br>
  gfm: true,          // GitHub Flavored Markdown
  sanitize: false,    // We'll handle sanitization ourselves if needed
  smartLists: true,   // Use smarter list behavior
  smartypants: false  // Don't use smart quotes (can cause issues in content scripts)
});

/**
 * Convert markdown text to HTML using the marked library
 * @param {string} markdown - The markdown text to convert
 * @returns {string} - The HTML representation of the markdown
 */
export const markdownToHtml = (markdown) => {
  if (!markdown || typeof markdown !== 'string') {
    return '';
  }

  try {
    // Use marked to convert markdown to HTML
    const html = marked.parse(markdown);
    return html;
  } catch (error) {
    console.error('Error parsing markdown:', error);
    // Fallback to plain text if parsing fails
    return `<p>${markdown.replace(/\n/g, '<br>')}</p>`;
  }
};
