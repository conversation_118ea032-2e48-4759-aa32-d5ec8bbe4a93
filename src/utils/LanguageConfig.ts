// Language configuration mapping values to labels
export interface LanguageOption {
  value: string;
  label: string;
  apiValue: string; // The value to be sent to the API
}

// UI Language options (for interface internationalization)
export const UI_LANGUAGE_OPTIONS: LanguageOption[] = [
  {
    value: 'en',
    label: 'English',
    apiValue: 'english'
  },
  {
    value: 'zh',
    label: '中文',
    apiValue: 'chinese'
  }
];

// AI Output Language options (for AI generation language)
export const AI_OUTPUT_LANGUAGE_OPTIONS: LanguageOption[] = [
  { value: 'en', label: 'English', apiValue: 'english' },
  { value: 'zh', label: '中文 (Chinese)', apiValue: 'chinese' },
  { value: 'es', label: 'Español (Spanish)', apiValue: 'spanish' },
  { value: 'fr', label: 'Français (French)', apiValue: 'french' },
  { value: 'de', label: '<PERSON><PERSON><PERSON> (German)', apiValue: 'german' },
  { value: 'ja', label: '日本語 (Japanese)', apiValue: 'japanese' },
  { value: 'ko', label: '한국어 (Korean)', apiValue: 'korean' },
  { value: 'pt', label: 'Português (Portuguese)', apiValue: 'portuguese' },
  { value: 'ru', label: 'Русский (Russian)', apiValue: 'russian' },
  { value: 'ar', label: 'العربية (Arabic)', apiValue: 'arabic' },
  { value: 'hi', label: 'हिन्दी (Hindi)', apiValue: 'hindi' },
  { value: 'it', label: 'Italiano (Italian)', apiValue: 'italian' },
  { value: 'nl', label: 'Nederlands (Dutch)', apiValue: 'dutch' },
  { value: 'sv', label: 'Svenska (Swedish)', apiValue: 'swedish' },
  { value: 'da', label: 'Dansk (Danish)', apiValue: 'danish' },
  { value: 'no', label: 'Norsk (Norwegian)', apiValue: 'norwegian' },
  { value: 'fi', label: 'Suomi (Finnish)', apiValue: 'finnish' },
  { value: 'pl', label: 'Polski (Polish)', apiValue: 'polish' },
  { value: 'tr', label: 'Türkçe (Turkish)', apiValue: 'turkish' },
  { value: 'he', label: 'עברית (Hebrew)', apiValue: 'hebrew' },
  { value: 'th', label: 'ไทย (Thai)', apiValue: 'thai' },
  { value: 'vi', label: 'Tiếng Việt (Vietnamese)', apiValue: 'vietnamese' },
  { value: 'id', label: 'Bahasa Indonesia (Indonesian)', apiValue: 'indonesian' },
  { value: 'ms', label: 'Bahasa Melayu (Malay)', apiValue: 'malay' },
  { value: 'tl', label: 'Filipino (Tagalog)', apiValue: 'filipino' },
  { value: 'uk', label: 'Українська (Ukrainian)', apiValue: 'ukrainian' },
  { value: 'cs', label: 'Čeština (Czech)', apiValue: 'czech' },
  { value: 'sk', label: 'Slovenčina (Slovak)', apiValue: 'slovak' },
  { value: 'hu', label: 'Magyar (Hungarian)', apiValue: 'hungarian' },
  { value: 'ro', label: 'Română (Romanian)', apiValue: 'romanian' },
  { value: 'bg', label: 'Български (Bulgarian)', apiValue: 'bulgarian' },
  { value: 'hr', label: 'Hrvatski (Croatian)', apiValue: 'croatian' },
  { value: 'sr', label: 'Српски (Serbian)', apiValue: 'serbian' },
  { value: 'sl', label: 'Slovenščina (Slovenian)', apiValue: 'slovenian' },
  { value: 'et', label: 'Eesti (Estonian)', apiValue: 'estonian' },
  { value: 'lv', label: 'Latviešu (Latvian)', apiValue: 'latvian' },
  { value: 'lt', label: 'Lietuvių (Lithuanian)', apiValue: 'lithuanian' },
  { value: 'el', label: 'Ελληνικά (Greek)', apiValue: 'greek' },
  { value: 'mt', label: 'Malti (Maltese)', apiValue: 'maltese' },
  { value: 'ga', label: 'Gaeilge (Irish)', apiValue: 'irish' },
  { value: 'cy', label: 'Cymraeg (Welsh)', apiValue: 'welsh' },
  { value: 'is', label: 'Íslenska (Icelandic)', apiValue: 'icelandic' },
  { value: 'fa', label: 'فارسی (Persian)', apiValue: 'persian' },
  { value: 'ur', label: 'اردو (Urdu)', apiValue: 'urdu' },
  { value: 'bn', label: 'বাংলা (Bengali)', apiValue: 'bengali' },
  { value: 'ta', label: 'தமிழ் (Tamil)', apiValue: 'tamil' },
  { value: 'te', label: 'తెలుగు (Telugu)', apiValue: 'telugu' },
  { value: 'mr', label: 'मराठी (Marathi)', apiValue: 'marathi' },
  { value: 'gu', label: 'ગુજરાતી (Gujarati)', apiValue: 'gujarati' },
  { value: 'kn', label: 'ಕನ್ನಡ (Kannada)', apiValue: 'kannada' },
  { value: 'ml', label: 'മലയാളം (Malayalam)', apiValue: 'malayalam' },
  { value: 'pa', label: 'ਪੰਜਾਬੀ (Punjabi)', apiValue: 'punjabi' }
];

// Legacy support - keep for backward compatibility
export const LANGUAGE_OPTIONS: LanguageOption[] = UI_LANGUAGE_OPTIONS;

// Helper function to get the API value from a UI language value
export const getUILanguageApiValue = (value: string): string => {
  const option = UI_LANGUAGE_OPTIONS.find(opt => opt.value === value);
  return option ? option.apiValue : 'english'; // Default to english if not found
};

// Helper function to get the API value from an AI output language value
export const getAIOutputLanguageApiValue = (value: string): string => {
  const option = AI_OUTPUT_LANGUAGE_OPTIONS.find(opt => opt.value === value);
  return option ? option.apiValue : 'english'; // Default to english if not found
};

// Legacy support - keep for backward compatibility
export const getLanguageApiValue = getUILanguageApiValue;

// Helper function to get UI language options for select components
export const getUILanguageOptions = (): LanguageOption[] => {
  return UI_LANGUAGE_OPTIONS;
};

// Helper function to get AI output language options for select components
export const getAIOutputLanguageOptions = (): LanguageOption[] => {
  return AI_OUTPUT_LANGUAGE_OPTIONS;
};

// Legacy support - keep for backward compatibility
export const getLanguageOptions = getUILanguageOptions;
