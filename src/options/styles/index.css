body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: #333;
  height: 100%;
  overflow: hidden;
  line-height: 1.6;
}

html {
  height: 100%;
  overflow: hidden;
}

/* Loading container */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: #666;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.loading-container::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Main container */
.options-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  backdrop-filter: blur(10px);
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Header */
.options-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  padding: 20px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.options-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
  pointer-events: none;
}

.logo {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 1;
}

.logo img {
  width: 40px;
  height: 40px;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
  transition: transform 0.3s ease;
}

.logo img:hover {
  transform: scale(1.05) rotate(5deg);
}

.logo h1 {
  font-size: 24px;
  margin: 0;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
  letter-spacing: -0.5px;
}

/* Login status */
.login-status {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.logged-in {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.15);
  padding: 8px 16px;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.username {
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
}

.logged-in-indicator {
  background-color: #4ade80;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.logged-out {
  display: flex;
  align-items: center;
  gap: 16px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.login-button {
  background: linear-gradient(135deg, #10a37f 0%, #0d8c6d 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(16, 163, 127, 0.3);
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 163, 127, 0.4);
}

.loading-indicator {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
}

/* Content area */
.options-content {
  display: flex;
  flex: 1;
  gap: 0;
}

/* Sidebar */
.options-sidebar {
  width: 280px;
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  border-right: none;
  padding: 32px 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100vh - 144px);
  overflow-y: auto;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.08);
  position: relative;
}

.options-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg, transparent 0%, rgba(102, 126, 234, 0.2) 50%, transparent 100%);
}

.options-sidebar nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.options-sidebar nav li {
  padding: 16px 32px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
  color: #4a5568;
  position: relative;
  margin: 4px 16px;
  border-radius: 12px;
}

.options-sidebar nav li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 4px 4px 0;
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.options-sidebar nav li:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  color: #2d3748;
  transform: translateX(4px);
}

.options-sidebar nav li.active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
  color: #667eea;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.options-sidebar nav li.active::before {
  transform: scaleY(1);
}

.sidebar-footer {
  padding: 24px 32px;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
  margin-top: 16px;
}

.pricing-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 20px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.pricing-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.pricing-button:hover::before {
  left: 100%;
}

.pricing-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Main content area */
.options-main {
  flex: 1;
  padding: 32px 40px;
  overflow-y: auto;
  height: calc(100vh - 144px);
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
}

.options-main h2 {
  font-size: 28px;
  margin-top: 0;
  margin-bottom: 32px;
  color: #2d3748;
  padding-bottom: 16px;
  border-bottom: 3px solid transparent;
  background: linear-gradient(90deg, #667eea, #764ba2) bottom / 60px 3px no-repeat;
  font-weight: 700;
  letter-spacing: -0.5px;
}

/* Form elements */
.form-group {
  margin-bottom: 32px;
  background: rgba(255, 255, 255, 0.8);
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

label {
  display: block;
  margin-bottom: 12px;
  font-weight: 600;
  color: #2d3748;
  font-size: 16px;
  letter-spacing: -0.2px;
}

input[type="text"],
select {
  width: 100%;
  max-width: 500px;
  padding: 14px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  box-sizing: border-box;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  color: #2d3748;
}

input[type="text"]:focus,
select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

button:hover::before {
  left: 100%;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Status messages */
.status {
  margin-top: 20px;
  padding: 12px;
  border-radius: 4px;
  font-size: 14px;
  max-width: 400px;
}

.status.success {
  background-color: #d4edda;
  color: #155724;
}

.status.error {
  background-color: #f8d7da;
  color: #721c24;
}

/* Account section */
.account-info {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  padding: 24px;
  border-radius: 16px;
  max-width: 600px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.account-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.account-info p {
  margin-bottom: 20px;
  color: #4a5568;
  line-height: 1.6;
}

.user-info-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

.account-info .user-row {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #2d3748;
}

/* Quota information section */
.quota-info {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  padding: 24px;
  border-radius: 16px;
  max-width: 600px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.quota-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10a37f, #0d8c6d);
}

.quota-info h3 {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

/* Plan and quota styles */
.plan-row {
  display: flex;
  align-items: center;
  padding-top: 24px;
  padding-bottom: 10px;
  flex-wrap: wrap;
  gap: 10px;
}

.plan-title {
  font-weight: 500;
  margin-right: 8px;
}

.plan-name {
  font-size: 22px;
  margin-right: 10px;
}

.upgrade-button {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(245, 158, 11, 0.3);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.upgrade-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.upgrade-button:hover::before {
  left: 100%;
}

.upgrade-button:hover {
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 5px 15px rgba(245, 158, 11, 0.4);
}

.quota-header {
  color: #666;
  font-size: 14px;
  margin-top: 16px;
  margin-bottom: 8px;
}

.quota-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quota-row {
  display: flex;
  align-items: center;
  padding-top: 10px;
}

.quota-title {
  font-weight: 500;
  margin-right: 8px;
  min-width: 100px;
}

.quota-value {
  font-size: 16px;
  font-weight: 500;
}

.loading-placeholder {
  margin-top: 16px;
  margin-bottom: 16px;
  color: #666;
  font-style: italic;
}

.open-aiflow-button {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #10a37f 0%, #0d8c6d 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  white-space: nowrap;
  box-shadow: 0 4px 15px rgba(16, 163, 127, 0.3);
  position: relative;
  overflow: hidden;
}

.open-aiflow-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.open-aiflow-button:hover::before {
  left: 100%;
}

.open-aiflow-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 163, 127, 0.4);
}

.tooltip {
  position: relative;
  display: inline-block;
  z-index: 100;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

.login-prompt {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 4px;
  max-width: 400px;
  text-align: center;
}

.login-prompt p {
  margin-bottom: 16px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .options-sidebar {
    width: 240px;
  }

  .options-main {
    padding: 24px 32px;
  }

  .form-group {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .options-content {
    flex-direction: column;
  }

  .options-sidebar {
    width: 100%;
    height: auto;
    padding: 16px 0;
  }

  .options-sidebar nav ul {
    display: flex;
    overflow-x: auto;
    gap: 8px;
    padding: 0 16px;
  }

  .options-sidebar nav li {
    white-space: nowrap;
    margin: 0;
    border-radius: 25px;
    padding: 12px 20px;
  }

  .options-main {
    padding: 20px;
    height: auto;
  }

  .options-main h2 {
    font-size: 24px;
  }

  .account-info, .quota-info {
    max-width: 100%;
  }

  .user-info-container {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .options-header {
    padding: 16px 20px;
  }

  .logo h1 {
    font-size: 20px;
  }

  .logo img {
    width: 32px;
    height: 32px;
  }

  .options-main {
    padding: 16px;
  }

  .form-group {
    padding: 16px;
  }

  input[type="text"],
  select {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    color: #f7fafc;
  }

  .loading-container {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  }

  .options-header {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  }

  .options-sidebar {
    background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%);
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.2);
  }

  .options-sidebar nav li {
    color: #cbd5e0;
  }

  .options-sidebar nav li:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    color: #e2e8f0;
  }

  .options-sidebar nav li.active {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
    color: #667eea;
  }

  .options-main {
    background: rgba(45, 55, 72, 0.7);
  }

  .options-main h2 {
    color: #f7fafc;
  }

  .form-group {
    background: rgba(45, 55, 72, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  label {
    color: #e2e8f0;
  }

  input[type="text"],
  select {
    background: rgba(26, 32, 44, 0.9);
    color: #f7fafc;
    border-color: #4a5568;
  }

  input[type="text"]:focus,
  select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
  }

  .account-info, .quota-info {
    background: linear-gradient(135deg, rgba(45, 55, 72, 0.9) 0%, rgba(26, 32, 44, 0.9) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .account-info .user-row {
    color: #e2e8f0;
  }

  .quota-info h3 {
    color: #f7fafc;
  }

  .quota-header {
    color: #cbd5e0;
  }

  .plan-title, .quota-title {
    color: #e2e8f0;
  }

  .plan-name, .quota-value {
    color: #f7fafc;
  }

  .loading-placeholder {
    color: #a0aec0;
  }
}
