/* Advanced Section Styles */
.advanced-content {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
  padding: 0;
}

.advanced-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  transition: all 0.4s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

.advanced-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
  pointer-events: none;
}

.advanced-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.18);
}

.card-header {
  padding: 2rem;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  background-color: deepskyblue;
  border-bottom: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
  pointer-events: none;
}

.card-header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 1.75rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
  letter-spacing: -0.5px;
  position: relative;
  z-index: 1;
}

.highlight-badge {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 0.875rem;
  font-weight: 700;
  box-shadow: 0 4px 15px rgba(74, 222, 128, 0.3);
  position: relative;
  z-index: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-content {
  padding: 2rem;
  position: relative;
  z-index: 1;
}

.main-message {
  font-size: 1.2rem;
  color: #2d3748;
  margin-bottom: 2rem;
  line-height: 1.7;
  font-weight: 500;
}

.feature-list {
  display: flex;
  flex-direction: row;
  gap: 1.5rem;
}

.aiflow-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2.5rem;
}

.feature-column h4 {
  color: #2d3748;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 700;
  letter-spacing: -0.3px;
}

.feature-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-column li {
  padding: 0.75rem 0;
  color: #4a5568;
  position: relative;
  padding-left: 2rem;
  font-size: 1rem;
  line-height: 1.6;
  transition: all 0.3s ease;
}

.feature-column li:hover {
  color: #2d3748;
  transform: translateX(4px);
}

.feature-column li::before {
  content: "✨";
  color: #667eea;
  position: absolute;
  left: 0;
  font-size: 1.2rem;
}

.cta-container {
  text-align: center;
  margin-top: 2rem;
}

.try-aiflow-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #4CAF50;
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: background-color 0.2s ease;
}

.try-aiflow-button:hover {
  background: #45a049;
}

.arrow {
  transition: transform 0.2s ease;
}

.try-aiflow-button:hover .arrow {
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .aiflow-features {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .card-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .feature-list {
    gap: 0.75rem;
  }
}

/* Advanced Section Additional Styles */
.advanced-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* .advanced-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
} */

.advanced-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.main-message {
  /* font-size: 1.2rem; */
  line-height: 1.6;
  color: #2c3e50;
  margin-bottom: 2rem;
}

.main-message strong {
  color: #4CAF50;
  font-weight: 600;
}

.feature-list {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 1.1rem;
  column-gap: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-weight: 500;
  color: #2d3748;
}

.feature-item:hover {
  transform: translateX(12px) translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  font-size: 1.5rem;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  color: white;
}

.aiflow-features {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-column {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-column:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.feature-column h4 {
  color: #2d3748;
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 3px solid transparent;
  background: linear-gradient(90deg, #667eea, #764ba2) bottom / 40px 3px no-repeat;
  font-weight: 700;
  letter-spacing: -0.3px;
}

.feature-column ul li {
  padding-left: 2rem;
  position: relative;
  line-height: 1.6;
  font-size: 1rem;
  color: #4a5568;
  transition: all 0.3s ease;
}

.feature-column ul li:hover {
  color: #2d3748;
  transform: translateX(4px);
}

.feature-column ul li::before {
  content: "🚀";
  color: #667eea;
  position: absolute;
  left: 0;
  font-size: 1.1rem;
}

.cta-container {
  margin-top: auto;
  padding-top: 1rem;
}

.try-aiflow-button {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.25rem 2.5rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 700;
  font-size: 1.2rem;
  transition: all 0.4s ease;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.try-aiflow-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.try-aiflow-button:hover::before {
  left: 100%;
}

.try-aiflow-button:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 12px 48px rgba(102, 126, 234, 0.4);
}

.arrow {
  transition: transform 0.4s ease;
  font-size: 1.3rem;
}

.try-aiflow-button:hover .arrow {
  transform: translateX(8px) rotate(15deg);
}

/* Dark mode support for advanced section */
@media (prefers-color-scheme: dark) {
  .advanced-card {
    background: linear-gradient(135deg, rgba(45, 55, 72, 0.95) 0%, rgba(26, 32, 44, 0.95) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .advanced-card::before {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  }

  .card-header {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  }

  .card-content {
    color: #e2e8f0;
  }

  .main-message {
    color: #f7fafc;
  }

  .feature-list {
    background: linear-gradient(135deg, rgba(26, 32, 44, 0.8) 0%, rgba(45, 55, 72, 0.8) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .feature-item {
    background: linear-gradient(135deg, rgba(45, 55, 72, 0.9) 0%, rgba(26, 32, 44, 0.9) 100%);
    color: #e2e8f0;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .aiflow-features {
    background: linear-gradient(135deg, rgba(26, 32, 44, 0.8) 0%, rgba(45, 55, 72, 0.8) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .feature-column {
    background: linear-gradient(135deg, rgba(45, 55, 72, 0.9) 0%, rgba(26, 32, 44, 0.9) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .feature-column h4 {
    color: #f7fafc;
  }

  .feature-column ul li {
    color: #cbd5e0;
  }

  .feature-column ul li:hover {
    color: #e2e8f0;
  }
}

/* Responsive Design Enhancements */
@media (max-width: 1024px) {
  .aiflow-features {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .card-header {
    padding: 1.5rem;
  }

  .card-content {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .advanced-section {
    padding: 1rem;
  }

  .advanced-content {
    gap: 1.5rem;
  }

  .card-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    padding: 1.25rem;
  }

  .card-header h3 {
    font-size: 1.5rem;
  }

  .card-content {
    padding: 1.25rem;
  }

  .feature-list {
    flex-direction: column;
    gap: 1rem;
  }

  .feature-item {
    padding: 1.25rem;
  }

  .feature-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.25rem;
  }

  .aiflow-features {
    padding: 1.25rem;
  }

  .feature-column {
    padding: 1.25rem;
  }

  .try-aiflow-button {
    width: 100%;
    justify-content: center;
    padding: 1rem 2rem;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .advanced-content {
    gap: 1rem;
  }

  .card-header {
    padding: 1rem;
  }

  .card-header h3 {
    font-size: 1.25rem;
  }

  .card-content {
    padding: 1rem;
  }

  .main-message {
    font-size: 1rem;
  }

  .feature-item {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
    padding: 1rem;
  }

  .feature-icon {
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
  }

  .feature-column h4 {
    font-size: 1.1rem;
  }

  .feature-column ul li {
    font-size: 0.9rem;
  }

  .try-aiflow-button {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}