import React, { useEffect, useState } from 'react';
import useI18n from '../../common/hooks/useI18n';
import useSettings from '../../common/hooks/useSettings';
import { APP_URL } from '../../utils/Constants';
import { UI_LANGUAGE_OPTIONS, AI_OUTPUT_LANGUAGE_OPTIONS } from '../../utils/LanguageConfig';
import './App.css';

// Define interface for quota item
interface QuotaItem {
  level: string;
  quota: string | number;
  remain?: number;
}

// Define interface for quota data
interface QuotaData {
  servingProduct?: {
    productId: string;
  };
  t1?: QuotaItem;
  t2?: QuotaItem;
  whiteboards?: QuotaItem;
  flowNodes?: QuotaItem;
  [key: string]: any; // Allow indexing with string
}

const App: React.FC = () => {
  const { getMessage } = useI18n();
  const { settings, loading, saveSettings, updateSetting } = useSettings();
  const [isLoggedIn, setIsLoggedIn] = useState<boolean | null>(null);
  const [username, setUsername] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('account');
  const [quotaData, setQuotaData] = useState<QuotaData | null>(null);
  const [loadingQuota, setLoadingQuota] = useState<boolean>(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    saveSettings().then((success) => {
      if (success) {
        console.log('Settings saved successfully');
      } else {
        console.error('Failed to save settings');
      }
    });
  };

  // Set document language based on current UI language setting or browser language
  useEffect(() => {
    const uiLang = settings.uiLanguage || settings.language; // Fallback to legacy language setting
    if (uiLang) {
      document.documentElement.lang = uiLang;
    } else {
      // Default to browser language
      const browserLang = navigator.language.split('-')[0];
      if (browserLang === 'zh') {
        updateSetting('uiLanguage', 'zh');
        // Also set AI output language if not set
        if (!settings.aiOutputLanguage) {
          updateSetting('aiOutputLanguage', 'zh');
        }
      } else {
        updateSetting('uiLanguage', 'en');
        // Also set AI output language if not set
        if (!settings.aiOutputLanguage) {
          updateSetting('aiOutputLanguage', 'en');
        }
      }
    }

    // Set page title
    document.title = getMessage('optionsTitle');
  }, [settings.uiLanguage, settings.language]);

  useEffect(() => {
    saveSettings();
  }, [settings])

  // Function to fetch quota information
  const fetchQuotaInfo = async () => {
    if (!isLoggedIn) return;

    setLoadingQuota(true);
    try {
      chrome.runtime.sendMessage({ action: 'getRemainDailyAIQueryQuotas' }, (response) => {
        setLoadingQuota(false);
        if (chrome.runtime.lastError) {
          console.error('Error fetching quota information:', chrome.runtime.lastError);
          return;
        }

        if (response && response.success && response.quotaData) {
          setQuotaData(response.quotaData);
        } else {
          console.error('Failed to fetch quota information:', response?.errorCode || 'Unknown error');
        }
      });
    } catch (error) {
      setLoadingQuota(false);
      console.error('Error fetching quota information:', error);
    }
  };

  // Check login status
  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        // Send message to background script to check login status
        chrome.runtime.sendMessage({ action: 'checkLoginStatus' }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('Error checking login status:', chrome.runtime.lastError);
            setIsLoggedIn(false);
          } else if (response && response.isLoggedIn) {
            setIsLoggedIn(true);
            setUsername(response.username || '');
          } else {
            setIsLoggedIn(false);
          }
        });
      } catch (error) {
        console.error('Error checking login status:', error);
        setIsLoggedIn(false);
      }
    };

    checkLoginStatus();
  }, []);

  // Fetch quota information when the activeTab changes to 'account'
  useEffect(() => {
    if (activeTab === 'account' && isLoggedIn) {
      fetchQuotaInfo();
    }
  }, [activeTab, isLoggedIn]);

  const handleLogin = () => {
    // Open login page in a new tab
    window.open(`${APP_URL}/#/login?source=extension`);
  };

  const openPricingPage = () => {
    // Open pricing page in a new tab
    window.open(`${APP_URL}/#/aiplans`, '_blank');
  };

  // Helper function to capitalize first letter of a string
  const capitalizeFirstLetter = (string: string) => {
    if (!string) return '';
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  if (loading) {
    return <div className="loading-container">{getMessage('loadingText')}</div>;
  }

  return (
    <div className="options-container">
      <header className="options-header">
        <div className="logo">
          <img src="../images/icon.svg" alt="AI Prompt Optimizer" />
          <h1>{getMessage('appName')}</h1>
        </div>

        <div className="login-status">
          {isLoggedIn === null ? (
            <div className="loading-indicator">{getMessage('loadingText')}</div>
          ) : isLoggedIn ? (
            <div className="logged-in">
              <span className="username">{username}</span>
              <span className="status-indicator logged-in-indicator"></span>
            </div>
          ) : (
            <div className="logged-out">
              <span>{getMessage('notLoggedIn')}</span>
              <button className="login-button" onClick={handleLogin}>
                {getMessage('loginButton')}
              </button>
            </div>
          )}
        </div>
      </header>

      <div className="options-content">
        <aside className="options-sidebar">
          <nav>
            <ul>
              <li
                className={activeTab === 'account' ? 'active' : ''}
                onClick={() => setActiveTab('account')}
              >
                {getMessage('accountSection')}
              </li>
              <li
                className={activeTab === 'preferences' ? 'active' : ''}
                onClick={() => setActiveTab('preferences')}
              >
                {getMessage('preferencesSection')}
              </li>
              <li
                className={activeTab === 'advanced' ? 'active' : ''}
                onClick={() => setActiveTab('advanced')}
              >
                {getMessage('advancedSection')}
              </li>
            </ul>
          </nav>

          <div className="sidebar-footer">
            <button className="pricing-button" onClick={openPricingPage}>
              {getMessage('pricingButton')}
            </button>
          </div>
        </aside>

        <main className="options-main">
          {activeTab === 'account' && (
            <section className="account-section">
              <h2>{getMessage('accountSection')}</h2>
              {isLoggedIn ? (
                <>
                  {/* User Account Information */}
                  <div className="account-info">
                    <div className="user-info-container">
                      <div className="user-row">
                        <strong>{getMessage('loginButton')}:</strong> {username}
                      </div>
                      <div>
                        <a href={`${APP_URL}`} target="_blank" className="open-aiflow-button">
                          {getMessage('openAIFlowButton')}
                        </a>
                        {/* <span className="tooltiptext">{getMessage('openAIFlowTooltip')}</span> */}
                      </div>
                    </div>
                  </div>

                  {/* Service Plan and Quota Information in a separate section */}
                  <div className="quota-info">
                    <h3>{getMessage('quotaSectionTitle') || 'Service Plan & Quota'}</h3>

                    {loadingQuota ? (
                      <div className="loading-indicator">{getMessage('loadingText')}</div>
                    ) : quotaData ? (
                      <>
                        {quotaData.servingProduct?.productId && (
                          <div className="plan-row">
                            <div className="plan-title">
                              {getMessage('currentPlanLabel') || 'Current Plan:'}
                            </div>
                            <div className="plan-name">
                              AI {capitalizeFirstLetter(quotaData.servingProduct.productId)}
                            </div>
                            <button
                              className="upgrade-button"
                              onClick={openPricingPage}
                            >
                              {getMessage('upgradePlanButton') || 'Upgrade Plan'}
                            </button>
                          </div>
                        )}

                        <div className="quota-header">
                          {getMessage('quotaHeaderLabel') || 'AI-related quotas (Remaining/Total):'}
                        </div>

                        <div className="quota-container">
                          {['t1', 't2'
                            // , 'whiteboards', 'flowNodes'
                          ].map((item, index) => {
                            const quotaItem = quotaData[item] as QuotaItem | undefined;
                            if (!quotaItem) return null;

                            return (
                              <div key={index} className="quota-row">
                                <div className="quota-title">
                                  {quotaItem.level}:
                                </div>
                                <div className="quota-value">
                                  {quotaItem.quota === 'unlimited' && (getMessage('unlimitedLabel') || 'Unlimited')}
                                  {quotaItem.quota !== 'unlimited' && (
                                    quotaItem.remain === undefined
                                      ? `${quotaItem.quota}`
                                      : `${quotaItem.remain}/${quotaItem.quota}`
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </>
                    ) : (
                      <div className="loading-placeholder">
                        {getMessage('noQuotaDataMessage') || 'No quota data available.'}
                      </div>
                    )}
                  </div>
                </>
              ) : (
                <div className="login-prompt">
                  <p>{getMessage('errorLoginRequired')}</p>
                  <button className="login-button" onClick={handleLogin}>
                    {getMessage('loginFunBlocksButton')}
                  </button>
                </div>
              )}
            </section>
          )}

          {activeTab === 'preferences' && (
            <section className="preferences-section">
              <h2>{getMessage('preferencesSection')}</h2>

              <form onSubmit={handleSubmit}>
                <div className="form-group">
                  <label htmlFor="uiLanguage">{getMessage('uiLanguageLabel')}</label>
                  <select
                    id="uiLanguage"
                    value={settings.uiLanguage || settings.language || 'en'}
                    onChange={(e) => {
                      updateSetting('uiLanguage', e.target.value);
                    }}
                  >
                    {UI_LANGUAGE_OPTIONS.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label htmlFor="aiOutputLanguage">{getMessage('aiOutputLanguageLabel')}</label>
                  <select
                    id="aiOutputLanguage"
                    value={settings.aiOutputLanguage || settings.language || 'en'}
                    onChange={(e) => {
                      updateSetting('aiOutputLanguage', e.target.value);
                    }}
                  >
                    {AI_OUTPUT_LANGUAGE_OPTIONS.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* <div className="form-group">
                  <button type="submit">{getMessage('saveButton')}</button>
                </div> */}
              </form>
            </section>
          )}

          {activeTab === 'advanced' && (
            <section className="preferences-section">
              <h2>{getMessage('advancedSection')}</h2>
              
              <div className="advanced-content">
                <div className="advanced-card critical-thinking">
                  <div className="card-header">
                    <h3>{getMessage('beyondPromptEngineering')}</h3>
                    <div className="highlight-badge">{getMessage('keyInsight')}</div>
                  </div>
                  <div className="card-content">
                    <p className="main-message">
                      {getMessage('criticalThinkingMessage')}
                    </p>
                    <div className="feature-list">
                      <div className="feature-item">
                        <span className="feature-icon">🎯</span>
                        <span>{getMessage('askBetterQuestions')}</span>
                      </div>
                      <div className="feature-item">
                        <span className="feature-icon">🔍</span>
                        <span>{getMessage('discoverNewPerspectives')}</span>
                      </div>
                      <div className="feature-item">
                        <span className="feature-icon">💡</span>
                        <span>{getMessage('generateRelatedTopics')}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="advanced-card aiflow-promo">
                  <div className="card-header">
                    <h3>{getMessage('introducingAIFlow')}</h3>
                    <div className="highlight-badge">{getMessage('featuredProduct')}</div>
                  </div>
                  <div className="card-content">
                    <p className="main-message">
                      {getMessage('aiflowMessage')}
                    </p>
                    <div className="aiflow-features">
                      <div className="feature-column">
                        <h4>{getMessage('enhancedThinking')}</h4>
                        <ul>
                          <li>{getMessage('criticalThinking')}</li>
                          <li>{getMessage('creativeThinking')}</li>
                          <li>{getMessage('visualOrganization')}</li>
                        </ul>
                      </div>
                      <div className="feature-column">
                        <h4>{getMessage('keyBenefits')}</h4>
                        <ul>
                          <li>{getMessage('infiniteCanvas')}</li>
                          <li>{getMessage('mindMapping')}</li>
                          <li>{getMessage('aiPoweredExploration')}</li>
                          <li>{getMessage('questionRefinement')}</li>
                          <li>{getMessage('topicExploration')}</li>
                        </ul>
                      </div>
                    </div>
                    <div className="cta-container">
                      <a 
                        href="https://www.funblocks.net/aiflow" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="try-aiflow-button"
                      >
                        {getMessage('tryAIFlowNow')}
                        <span className="arrow">→</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          )}
        </main>
      </div>
    </div>
  );
};

export default App;
